/**
 * Canvas Navigation Helper
 * 
 * Provides navigation utilities and breadcrumbs for the Visual Canvas integration.
 * Helps users understand their current location and navigate between different views.
 */

import React from 'react';
import { 
  ArrowLeft, 
  Home, 
  Brain, 
  ChevronRight,
  Layers,
  Settings,
  HelpCircle
} from 'lucide-react';
import { useNavigationStore } from '@/stores/useNavigationStore';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface CanvasNavigationHelperProps {
  className?: string;
  showBreadcrumbs?: boolean;
  showQuickActions?: boolean;
}

export const CanvasNavigationHelper: React.FC<CanvasNavigationHelperProps> = ({
  className,
  showBreadcrumbs = true,
  showQuickActions = true,
}) => {
  const { mainTab, setMainTab, canvasMode, setCanvasMode } = useNavigationStore();

  const breadcrumbs = [
    { label: 'Home', icon: Home, tab: 'analyze' as const },
    { label: 'Visual Canvas', icon: Brain, tab: 'canvas' as const },
  ];

  const canvasModes = [
    { id: 'standard', label: 'Enhanced', description: 'Full-featured 3D visualization' },
    { id: 'safe', label: 'Safe Mode', description: 'Simplified rendering' },
    { id: 'chat-analysis', label: 'Chat Analysis', description: 'Specialized analysis view' },
  ];

  const currentMode = canvasModes.find(mode => mode.id === canvasMode);

  const handleNavigateBack = () => {
    setMainTab('analyze');
  };

  const handleNavigateHome = () => {
    setMainTab('analyze');
  };

  return (
    <div className={cn("flex items-center justify-between p-4 bg-card/50 backdrop-blur-sm border-b border-border", className)}>
      {/* Breadcrumbs and Navigation */}
      <div className="flex items-center gap-4">
        {showBreadcrumbs && (
          <nav className="flex items-center space-x-2 text-sm">
            {breadcrumbs.map((crumb, index) => {
              const Icon = crumb.icon;
              const isActive = crumb.tab === mainTab;
              const isLast = index === breadcrumbs.length - 1;

              return (
                <React.Fragment key={crumb.tab}>
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    size="sm"
                    onClick={() => setMainTab(crumb.tab)}
                    className={cn(
                      "flex items-center gap-2",
                      isActive && "bg-primary/10 text-primary"
                    )}
                  >
                    <Icon className="h-4 w-4" />
                    {crumb.label}
                  </Button>
                  {!isLast && (
                    <ChevronRight className="h-4 w-4 text-muted-foreground" />
                  )}
                </React.Fragment>
              );
            })}
          </nav>
        )}

        {/* Canvas Mode Indicator */}
        {mainTab === 'canvas' && currentMode && (
          <div className="flex items-center gap-2 ml-4 pl-4 border-l border-border">
            <Badge variant="outline" className="flex items-center gap-1">
              <Layers className="h-3 w-3" />
              {currentMode.label}
            </Badge>
            <span className="text-xs text-muted-foreground hidden sm:inline">
              {currentMode.description}
            </span>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      {showQuickActions && (
        <div className="flex items-center gap-2">
          {mainTab === 'canvas' && (
            <>
              {/* Canvas Mode Switcher */}
              <div className="hidden md:flex items-center gap-1 mr-2">
                {canvasModes.map((mode) => (
                  <Button
                    key={mode.id}
                    variant={canvasMode === mode.id ? "secondary" : "ghost"}
                    size="sm"
                    onClick={() => setCanvasMode(mode.id as 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated')}
                    className="text-xs"
                  >
                    {mode.label}
                  </Button>
                ))}
              </div>

              {/* Canvas Controls */}
              <Button variant="ghost" size="sm">
                <Settings className="h-4 w-4" />
              </Button>
              
              <Button variant="ghost" size="sm">
                <HelpCircle className="h-4 w-4" />
              </Button>
            </>
          )}

          {/* Back Button */}
          {mainTab === 'canvas' && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleNavigateBack}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">Back to Analysis</span>
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

// Canvas Status Indicator Component
export const CanvasStatusIndicator: React.FC<{
  isLoading?: boolean;
  nodeCount?: number;
  connectionCount?: number;
  className?: string;
}> = ({
  isLoading = false,
  nodeCount = 0,
  connectionCount = 0,
  className,
}) => {
  return (
    <div className={cn("flex items-center gap-4 text-sm text-muted-foreground", className)}>
      {isLoading ? (
        <div className="flex items-center gap-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
          <span>Loading canvas...</span>
        </div>
      ) : (
        <>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span>Ready</span>
          </div>
          {nodeCount > 0 && (
            <span>{nodeCount} nodes</span>
          )}
          {connectionCount > 0 && (
            <span>{connectionCount} connections</span>
          )}
        </>
      )}
    </div>
  );
};

// Canvas Quick Switch Component
export const CanvasQuickSwitch: React.FC<{
  className?: string;
}> = ({ className }) => {
  const { mainTab, setMainTab } = useNavigationStore();

  if (mainTab === 'canvas') {
    return null; // Don't show when already on canvas
  }

  return (
    <Button
      onClick={() => setMainTab('canvas')}
      variant="outline"
      size="sm"
      className={cn("fixed bottom-4 right-4 z-50 shadow-lg", className)}
    >
      <Brain className="h-4 w-4 mr-2" />
      Canvas
    </Button>
  );
};

export default CanvasNavigationHelper;
