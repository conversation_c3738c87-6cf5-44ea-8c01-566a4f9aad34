
import { create } from 'zustand';

type MainTab = 'analyze' | 'insights' | 'canvas';
type HistoricalAnalysisTab = 'analytics' | 'visual' | 'list';
type CanvasMode = 'standard' | 'safe' | 'chat-analysis' | 'figma-integrated';

interface NavigationState {
  mainTab: MainTab;
  setMainTab: (tab: MainTab) => void;
  historicalAnalysisTab: HistoricalAnalysisTab;
  setHistoricalAnalysisTab: (tab: HistoricalAnalysisTab) => void;
  // Canvas-specific navigation state
  canvasMode: CanvasMode;
  setCanvasMode: (mode: CanvasMode) => void;
}

export const useNavigationStore = create<NavigationState>((set) => ({
  mainTab: 'analyze',
  setMainTab: (tab) => set({ mainTab: tab }),
  historicalAnalysisTab: 'analytics',
  setHistoricalAnalysisTab: (tab) => set({ historicalAnalysisTab: tab }),
  canvasMode: 'figma-integrated',
  setCanvasMode: (mode) => set({ canvasMode: mode }),
}));
