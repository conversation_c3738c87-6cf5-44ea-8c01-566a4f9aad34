import React, { useRef, useEffect, useState, useCallback } from 'react';
import * as THREE from 'three';
// @ts-expect-error - OrbitControls is not typed in @types/three, or path is problematic
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GUI } from 'dat.gui';
import { createLights } from './sceneElements';
// Make sure SimpleNode is imported if it's defined in types.ts, or ensure its properties are known here
import { CanvasData, VisualConnection, SimpleNode, ConnectionData, NodeData } from './types';
import { createCurvedLineGeometry, getEdgeColorByStrength } from './edgeUtils';
import { useForceDirectedLayout, ForceDirectedLayoutOptions } from './useForceDirectedLayout';
import {
  createParticleField,
  updateParticleField,
  createGradientBackground,
  updateGradientBackground,
  BackgroundEffectOptions
} from './backgroundEffects';
import { ContextualInfoPanel } from './ContextualInfoPanel';
import {
  highlightSubgraph,
  clearSubgraphHighlight,
  NeighborHighlightOptions
} from './subgraphHighlight';
import { FilterSearchPanel } from './FilterSearchPanel';
import { useChatAnalysisCanvas } from '@/hooks/useChatAnalysisCanvas';
import { useCanvasRealtimeUpdates } from '@/hooks/useRealtimeUpdates';
import { useLangChainIntegration } from '@/hooks/useLangChainIntegration';
import { CanvasOptimization } from '@/services/performance/canvasOptimization';
// Performance imports (for future use)
// import { performanceProfiler } from '@/services/performance/performanceProfiler';
import { useDataProcessingWorker } from '@/hooks/useWebWorker';
import { ChatAnalysisNodeMenu, ChatAnalysisNodeMenuAction } from './ChatAnalysisNodeMenu';
import { NodeConnectionManager } from './NodeConnectionManager';
import { ClusterManager } from './ClusterManager';
import { ClusterVisualization } from './ClusterVisualization';
import { ChatSimulationManager } from './ChatSimulationManager';
import { SimulationRunner } from './SimulationRunner';
import { AdvancedControlPanel } from './AdvancedControlPanel';
import { OnboardingTour, useOnboardingTour } from './OnboardingTour';
import { KeyboardShortcutsManager, useKeyboardShortcuts, defaultCanvasShortcuts } from './KeyboardShortcutsManager';
import { ContextualHelpSystem, useContextualHelp } from './ContextualHelpSystem';
import { StatusTooltip } from './EnhancedTooltip';
import { AnalysisResult } from '@/types/conversation';
import { throttleFn } from '@/utils/throttleFn';
import { useCanvasEventHandlers } from './useCanvasEventHandlers';
import { useBackgroundEffects } from './useBackgroundEffects';

interface LivingDataCanvasProps {
  initialData?: CanvasData;
  enableChatAnalysisIntegration?: boolean; // New prop to enable chat analysis integration
}

// DraggedNodeInfo remains the same
interface DraggedNodeInfo {
  nodeId: string;
  offset: THREE.Vector2;
}

const LivingDataCanvas: React.FC<LivingDataCanvasProps> = ({
  initialData,
  enableChatAnalysisIntegration = false
}) => {
  // Chat Analysis Canvas Integration
  const chatAnalysisCanvas = useChatAnalysisCanvas();
  const { canvasData: chatCanvasData, isLoading: isChatDataLoading, getAnalysisRecordByNodeId } = chatAnalysisCanvas;

  // Real-time Updates Integration
  const realtimeUpdates = useCanvasRealtimeUpdates();
  const { connectionStatus, isConnected, realtimeUpdates: liveUpdates } = realtimeUpdates;

  // LangChain and LangFlow Integration
  const langChainIntegration = useLangChainIntegration();
  const {
    generateAnalysisInsights,
    generateClusterInsights,
    generateSimulationPrompts,
    analyzeConnections,
    isLangFlowAvailable,
    status: langChainStatus,
    error: langChainError
  } = langChainIntegration;

  // Performance optimization hooks (for future use)
  // const dataProcessingWorker = useDataProcessingWorker();
  const mountRef = useRef<HTMLDivElement>(null);
  const sceneRef = useRef<THREE.Scene | null>(null);
  const cameraRef = useRef<THREE.OrthographicCamera | null>(null);
  const rendererRef = useRef<THREE.WebGLRenderer | null>(null);
  const controlsRef = useRef<OrbitControls | null>(null);
  
  const [nodes, setNodes] = useState<Map<string, SimpleNode>>(new Map());
  const [visualConnections, setVisualConnections] = useState<Map<string, VisualConnection>>(new Map());

  const raycaster = useRef(new THREE.Raycaster());
  const mouse = useRef(new THREE.Vector2());
  const worldMouse = useRef(new THREE.Vector3());
  const [hoveredNodeId, setHoveredNodeId] = useState<string | null>(null);
  const [selectedNodeId, setSelectedNodeId] = useState<string | null>(null);
  const [draggedNodeInfo, setDraggedNodeInfo] = useState<DraggedNodeInfo | null>(null);
  const guiRef = useRef<GUI | null>(null);
  const originalNodePositionsRef = useRef<Map<string, THREE.Vector2>>(new Map());
  // Background effects
  const { particleFieldRef, backgroundMeshRef } = useBackgroundEffects(sceneRef, cameraRef);
  const clockRef = useRef(new THREE.Clock());
  // Info panel state
  const [showInfoPanel, setShowInfoPanel] = useState(false);
  const [infoPanelPosition, setInfoPanelPosition] = useState({ x: 0, y: 0 });
  // Subgraph highlighting state
  const [highlightOptions] = useState<NeighborHighlightOptions>({
    highlightDistance: 1,
    dimUnrelated: true,
    highlightEdges: true,
    highlightColor: 0x00ff88,
    dimOpacity: 0.3
  });

  // Search and filtering state
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  // const [searchResults, setSearchResults] = useState<string[]>([]);
  // const [filteredNodes, setFilteredNodes] = useState<string[]>([]);

  // Contextual menu state
  const [showContextMenu, setShowContextMenu] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({ x: 0, y: 0 });
  const [contextMenuNode, setContextMenuNode] = useState<SimpleNode | null>(null);

  // Connection creation state
  const [isCreatingConnection, setIsCreatingConnection] = useState(false);
  const [connectionSourceNode, setConnectionSourceNode] = useState<SimpleNode | null>(null);
  const [connectionTargetNode, setConnectionTargetNode] = useState<SimpleNode | null>(null);
  const [showConnectionManager, setShowConnectionManager] = useState(false);
  const [connectionManagerPosition, setConnectionManagerPosition] = useState({ x: 0, y: 0 });

  // Cluster management state
  const [selectedNodes, setSelectedNodes] = useState<Set<string>>(new Set());
  const [showClusterManager, setShowClusterManager] = useState(false);
  const [clusterManagerPosition, setClusterManagerPosition] = useState({ x: 0, y: 0 });
  const [editingCluster, setEditingCluster] = useState<string | null>(null);
  const clusterVisualizationRef = useRef<ClusterVisualization | null>(null);

  // Simulation management state
  const [showSimulationManager, setShowSimulationManager] = useState(false);
  const [simulationManagerPosition, setSimulationManagerPosition] = useState({ x: 0, y: 0 });
  const [showSimulationRunner, setShowSimulationRunner] = useState(false);
  const [runningSimulation, setRunningSimulation] = useState<string | null>(null);

  // Advanced controls state
  const [showAdvancedControls, setShowAdvancedControls] = useState(false);
  const [currentTheme, setCurrentTheme] = useState('dark');
  const [visibilitySettings, setVisibilitySettings] = useState({
    nodes: true,
    connections: true,
    clusters: true,
    labels: true,
    background: true,
    ui: true
  });

  // Onboarding tour
  const { showTour, completeTour, skipTour } = useOnboardingTour();

  // Keyboard shortcuts
  const keyboardShortcuts = useKeyboardShortcuts(defaultCanvasShortcuts);
  const {
    isVisible: showShortcuts,
    toggleVisibility: toggleShortcuts,
    handleShortcutExecute
  } = keyboardShortcuts;

  // Contextual help system
  const contextualHelp = useContextualHelp();
  const {
    isVisible: showHelp,
    currentContext,
    toggleVisibility: toggleHelp,
    setContext: setHelpContext
  } = contextualHelp;

  // Force-directed layout integration
  const initialLayoutOptions: ForceDirectedLayoutOptions = {
    repulsionStrength: 50,
    attractionStrength: 0.05,
    restLength: 5,
    dampingFactor: 0.95,
    maxSpeed: 1,
    centerForceStrength: 0.01,
    iterationsPerFrame: 1, // Default to 1 iteration
  };

  const [layoutOptions, setLayoutOptions] = useState<ForceDirectedLayoutOptions>(initialLayoutOptions);
  
  // Stable ref for dat.gui to bind to, initialized with current layoutOptions
  // This needs to be mutable and hold the values that GUI sliders directly manipulate.
  const guiControlParamsRef = useRef<ForceDirectedLayoutOptions>({...layoutOptions});
  // --- Web Worker Offloading for Force-Directed Layout ---
  const {
    calculateForceDirectedLayout,
    isWorkerAvailable
  } = useDataProcessingWorker();

  // Remove useForceDirectedLayout and related state
  // const {
  //   step: stepLayout,
  //   isRunning: isLayoutRunning,
  //   setIsRunning: setIsLayoutRunning
  // } = useForceDirectedLayout(nodes, visualConnections, layoutOptions);
  const [isLayoutRunning, setIsLayoutRunning] = useState(true);
  const [workerLayoutPending, setWorkerLayoutPending] = useState(false);

  // Helper to prepare data for the worker
  const getWorkerLayoutData = () => {
    // Convert nodes and connections to plain objects for the worker
    const nodeArr = Array.from(nodes.values()).map(node => ({
      id: node.id,
      position: { x: node.position.x, y: node.position.y },
    }));
    const connArr = Array.from(visualConnections.values()).map(conn => ({
      sourceId: conn.sourceNode.id,
      targetId: conn.targetNode.id,
    }));
    return { nodeArr, connArr };
  };

  // Throttle worker layout updates (e.g., every 2nd frame)
  const workerFrameCounter = useRef(0);

  // Refs for tracking animation and performance
  const animationFrameIdRef = useRef<number>();
  const performanceMonitorRef = useRef(CanvasOptimization.createPerformanceMonitor());
  const [performanceMetrics, setPerformanceMetrics] = useState({ fps: 60, frameTime: 16, memoryUsage: 0 });

  // Custom createRenderer function (simplified from sceneElements.ts)
  const createRenderer = (mount: HTMLDivElement): THREE.WebGLRenderer => {
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    renderer.setSize(mount.clientWidth, mount.clientHeight);
    renderer.setPixelRatio(window.devicePixelRatio);
    mount.appendChild(renderer.domElement);
    return renderer;
  };
  
  // Custom createOrthographicCamera function
  const createOrthographicCamera = (mount: HTMLDivElement): THREE.OrthographicCamera => {
    const aspect = mount.clientWidth / mount.clientHeight;
    const frustumSize = 20; // Adjust as needed for initial zoom
    const camera = new THREE.OrthographicCamera(
      frustumSize * aspect / -2,
      frustumSize * aspect / 2,
      frustumSize / 2,
      frustumSize / -2,
      1,  // Near plane
      1000 // Far plane
    );
    camera.position.z = 10; // Position camera looking along Z-axis
    return camera;
  };

  // Initialize Scene, Camera, Renderer, Lights, Controls
  useEffect(() => {
    if (!mountRef.current) return;

    // Update guiControlParamsRef.current whenever layoutOptions state changes
    // This ensures that if layoutOptions is set programmatically (e.g., by presets),
    // the ref used by dat.gui is also updated.
    guiControlParamsRef.current = {...layoutOptions};    sceneRef.current = new THREE.Scene();
    // Enhanced gradient background with dynamic color transitions
    sceneRef.current.background = new THREE.Color(0x0a0a1e); // Deeper, richer dark background

    cameraRef.current = createOrthographicCamera(mountRef.current);
    rendererRef.current = createRenderer(mountRef.current);
    
    createLights(sceneRef.current); // Ambient and directional lights are still useful

    // OrbitControls for an orthographic camera need to be configured for 2D-like interaction
    controlsRef.current = new OrbitControls(cameraRef.current, rendererRef.current.domElement);
    controlsRef.current.enableRotate = false; // Disable rotation for 2D
    controlsRef.current.mouseButtons = {
      LEFT: THREE.MOUSE.PAN, // Use left mouse for panning
      MIDDLE: THREE.MOUSE.DOLLY, // Middle mouse for zoom
      RIGHT: THREE.MOUSE.PAN // Optional: Right mouse for panning too
    };
    controlsRef.current.touches = {
      ONE: THREE.TOUCH.PAN,
      TWO: THREE.TOUCH.DOLLY_PAN
    };
    controlsRef.current.screenSpacePanning = true; // Pan in screen space    let animationFrameId: number;
    let animationTime = 0;
    let lastFrameTime = 0;
    const targetFPS = 30; // Limit to 30fps for better performance
    const frameInterval = 1000 / targetFPS;

    const animate = (currentTime: number = 0) => {
      animationFrameIdRef.current = requestAnimationFrame(animate);
      
      // Frame rate limiting for better performance
      if (currentTime - lastFrameTime < frameInterval) {
        return;
      }
      lastFrameTime = currentTime;
      
      const deltaTime = clockRef.current.getDelta();
      animationTime += deltaTime;if (isLayoutRunning && isWorkerAvailable() && !workerLayoutPending) {
        workerFrameCounter.current = (workerFrameCounter.current + 1) % 2; // Throttle: every 2nd frame
        if (workerFrameCounter.current === 0) {
          setWorkerLayoutPending(true);
          const { nodeArr, connArr } = getWorkerLayoutData();
          calculateForceDirectedLayout(nodeArr, connArr, layoutOptions)
            .then(result => {
              // Update node positions from worker result
              result.nodes.forEach((n: NodeData) => {
                const node = nodes.get(n.id);
                if (node && !node.isDragged) {
                  node.position.x = n.position?.x ?? node.position.x;
                  node.position.y = n.position?.y ?? node.position.y;
                  node.mesh.position.set(n.position?.x ?? node.position.x, n.position?.y ?? node.position.y, 0);
                }
              });
            })
            .catch(err => {
              console.error('Worker layout error:', err);
            })
            .finally(() => setWorkerLayoutPending(false));
        }
      }// Update background effects (throttled for performance)
      if (particleFieldRef.current && animationTime % 0.05 < deltaTime) { // Update ~20fps instead of 60fps
        updateParticleField(particleFieldRef.current, deltaTime);
      }
      if (backgroundMeshRef.current && animationTime % 0.033 < deltaTime) { // Update ~30fps
        updateGradientBackground(backgroundMeshRef.current, animationTime);
      }

      // Enhanced idle animations for nodes with performance optimization
      // Only update node animations every 3rd frame for better performance
      if (Math.floor(animationTime * 60) % 3 === 0) {
        nodes.forEach(node => {
          if (!node.isDragged) {
            const nodeValue = node.data.value || 50;
            // More refined pulsing based on node value and category
            const category = node.data.category || 'default';
            const categoryMultiplier = {
              'core': 1.5,     // Core nodes pulse more prominently
              'input': 1.2,    // Input nodes have moderate pulsing
              'process': 1.0,  // Standard pulsing
              'output': 1.1,   // Slightly more visible output
              'interface': 1.3, // Interface nodes stand out
              'storage': 0.8,  // Storage nodes pulse less
              'default': 1.0
            }[category] || 1.0;
            
            const pulseIntensity = (nodeValue / 100) * 0.08 * categoryMultiplier + 0.02; // Reduced intensity
            const pulseSpeed = 1.2 + (nodeValue / 100) * 0.6; // Reduced speed
            const uniqueOffset = parseFloat(node.id.replace(/\D/g, '') || '1') * 0.4;
            const scale = 1.0 + Math.sin(animationTime * pulseSpeed + uniqueOffset) * pulseIntensity;
            
            if (!hoveredNodeId || hoveredNodeId !== node.id) {
              node.mesh.scale.setScalar(scale);
            }
            
            // Enhanced breathing effect with color tinting (reduced frequency)
            const material = node.mesh.material as THREE.MeshBasicMaterial;
            if (!material.userData.isHighlighted) {
              const breathe = 0.85 + Math.sin(animationTime * 1.0 + uniqueOffset) * 0.1; // Reduced animation
              material.opacity = Math.min(breathe, node.mesh.userData.originalOpacity || 0.9);
              
              // Subtle color breathing effect (reduced)
              const originalColor = new THREE.Color(node.mesh.userData.originalColor);
              const breatheColor = originalColor.clone().lerp(new THREE.Color(0xffffff), Math.sin(animationTime * 0.3 + uniqueOffset) * 0.03);
              material.color.copy(breatheColor);
            }
          }
        });
      }

      // Update visual connections (lines) based on potentially new node positions
      visualConnections.forEach(conn => conn.update());
      
      controlsRef.current?.update(); // Still useful for pan/zoom
      
      // Performance monitoring and optimizations
      const monitor = performanceMonitorRef.current;
      monitor.update();

      // Apply performance optimizations for large datasets
      if (enableChatAnalysisIntegration && nodes.size > 100) {
        CanvasOptimization.applyNodeLOD(
          nodes,
          cameraRef.current!,
          { width: window.innerWidth, height: window.innerHeight }
        );

        CanvasOptimization.optimizeConnections(
          visualConnections,
          cameraRef.current!,
          nodes
        );
      }

      // Update performance metrics periodically
      if (monitor.getMetrics().frameCount % 60 === 0) {
        setPerformanceMetrics(monitor.getMetrics());
      }

      // Render the scene
      rendererRef.current?.render(sceneRef.current!, cameraRef.current!);
    };

    animate();    // Enhanced dat.gui setup with improved styling
    if (!guiRef.current) {        guiRef.current = new GUI({ autoPlace: false }); // Prevent auto-placement
        // Enhanced z-index and custom styling for dat.gui
        const guiElement = guiRef.current.domElement; // Use domElement directly

        // Ensure mountRef.current exists before appending
        if (mountRef.current) {
          // Create a container for the GUI within the canvas mount point
          // This helps in positioning it relative to the canvas
          const guiContainerWrapper = document.createElement('div');
          guiContainerWrapper.style.position = 'absolute'; // Position relative to mountRef
          guiContainerWrapper.style.top = '10px'; // Adjust as needed
          guiContainerWrapper.style.right = '10px'; // Adjust as needed
          guiContainerWrapper.style.zIndex = '100'; // High z-index
          guiContainerWrapper.appendChild(guiElement);
          mountRef.current.appendChild(guiContainerWrapper);
        }
        
        // Apply custom styling to the GUI element itself
        guiElement.style.fontFamily = 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
        // Add glassmorphism effect to dat.gui
        guiElement.style.background = 'rgba(30, 30, 45, 0.75)'; // Darker, slightly more opaque
        guiElement.style.backdropFilter = 'blur(12px) saturate(150%)'; // Enhanced blur and saturation
        guiElement.style.border = '1px solid rgba(255, 255, 255, 0.15)'; // Slightly more visible border
        guiElement.style.borderRadius = '10px'; // Consistent border radius
        // Ensure the GUI doesn't interfere with floating buttons
        guiElement.style.maxHeight = 'calc(100vh - 80px)'; // Adjusted max height
        guiElement.style.overflowY = 'auto'; // Changed to overflowY
        guiElement.style.overflowX = 'hidden'; // Hide horizontal scrollbar
        guiElement.style.boxShadow = '0 8px 32px 0 rgba( 31, 38, 135, 0.37 )'; // Softer shadow

        // Style individual GUI components (optional, if needed)
        // Example: guiRef.current.domElement.querySelectorAll('.cr.boolean input[type="checkbox"]').forEach(el => ...);
        
        const generalFolder = guiRef.current.addFolder('Canvas Controls');
        generalFolder.add({ resetView: () => {
          // Reset camera to initial position and zoom
          if (cameraRef.current && mountRef.current) {
            const aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
            const frustumSize = 20;
            cameraRef.current.left = frustumSize * aspect / -2;
            cameraRef.current.right = frustumSize * aspect / 2;
            cameraRef.current.top = frustumSize / 2;
            cameraRef.current.bottom = frustumSize / -2;
            cameraRef.current.position.set(0,0,10);
            cameraRef.current.zoom = 1;
            cameraRef.current.updateProjectionMatrix();
            controlsRef.current?.reset();
          }
          resetNodePositions();
          setLayoutOptions(initialLayoutOptions); // Reset layout options to default
        }}, 'resetView').name('Reset View & Nodes');        // Enhanced Visual Styling Controls with modern UI design
        const visualFolder = guiRef.current.addFolder('🎨 Visual Styling');
        const visualControls = {
          enableAnimations: true,
          animationSpeed: 1.0,
          pulseIntensity: 0.12,
          breathingEffect: true,
          nodeOpacity: 0.9,
          glowEffect: true,
          particleDensity: 200,
          backgroundIntensity: 0.4,
        };
        
        visualFolder.add(visualControls, 'enableAnimations').name('✨ Enable Animations');
        visualFolder.add(visualControls, 'animationSpeed', 0.1, 3.0, 0.1).name('🎬 Animation Speed');
        visualFolder.add(visualControls, 'pulseIntensity', 0.0, 0.3, 0.01).name('💓 Pulse Intensity');
        visualFolder.add(visualControls, 'breathingEffect').name('🌊 Breathing Effect');
        visualFolder.add(visualControls, 'glowEffect').name('✨ Glow Effects').onChange(val => {
          nodes.forEach(node => {
            // Add/remove glow effect based on toggle
            const ringMesh = node.mesh.children[0] as THREE.Mesh;
            if (ringMesh && ringMesh.material) {
              const ringMaterial = ringMesh.material as THREE.MeshBasicMaterial;
              ringMaterial.opacity = val ? 0.4 : 0.1;
            }
          });
        });
        visualFolder.add(visualControls, 'nodeOpacity', 0.3, 1.0, 0.1).name('👻 Node Opacity').onChange(val => {
          nodes.forEach(node => {
            const material = node.mesh.material as THREE.MeshBasicMaterial;
            node.mesh.userData.originalOpacity = val;
            if (!material.userData.isHighlighted) {
              material.opacity = val;
            }
          });
        });
        visualFolder.add(visualControls, 'particleDensity', 50, 500, 10).name('⭐ Particle Density').onChange(val => {
          // Update particle system density (would need to recreate particles)
          console.log('Particle density updated to:', val);
        });        visualFolder.add(visualControls, 'backgroundIntensity', 0.1, 0.8, 0.1).name('🌌 Background Intensity').onChange(val => {
          if (particleFieldRef.current) {
            const material = particleFieldRef.current.material as THREE.PointsMaterial;
            material.opacity = val;
          }
        });
        
        // Theme presets for quick visual changes
        const themePresets = {
          "🌊 Ocean": () => updateTheme(0x0ea5e9, 0x06b6d4, 0x0891b2),
          "🌸 Sakura": () => updateTheme(0xf472b6, 0xec4899, 0xdb2777),
          "🌿 Forest": () => updateTheme(0x10b981, 0x059669, 0x047857),
          "🔥 Ember": () => updateTheme(0xf97316, 0xea580c, 0xdc2626),
          "💎 Crystal": () => updateTheme(0x8b5cf6, 0x7c3aed, 0x6d28d9),
          "🌙 Midnight": () => updateTheme(0x64748b, 0x475569, 0x334155),
        };
        
        const updateTheme = (primary: number, secondary: number, accent: number) => {
          nodes.forEach(node => {
            const material = node.mesh.material as THREE.MeshBasicMaterial;
            if (!material.userData.isHighlighted) {
              const colors = [primary, secondary, accent];
              const randomColor = colors[Math.floor(Math.random() * colors.length)];
              material.color.set(randomColor);
              node.mesh.userData.originalColor = randomColor;
            }
          });
        };
        
        Object.entries(themePresets).forEach(([name, func]) => {
          visualFolder.add({ apply: func }, 'apply').name(name);
        });

        const layoutFolder = guiRef.current.addFolder('Layout Controls');
          // Define Creative Layout Presets for Data Analysis
        const layoutPresets: Record<string, ForceDirectedLayoutOptions> = {
          "🔍 Default View": initialLayoutOptions,
          "🎯 Tight Clusters": { repulsionStrength: 30, attractionStrength: 0.15, restLength: 2.5, dampingFactor: 0.9, maxSpeed: 0.8, centerForceStrength: 0.02, iterationsPerFrame: 1 },
          "🌐 Loose Exploration": { repulsionStrength: 120, attractionStrength: 0.02, restLength: 12, dampingFactor: 0.98, maxSpeed: 1.5, centerForceStrength: 0.005, iterationsPerFrame: 1 },
          "⚡ Quick Settle": { repulsionStrength: 60, attractionStrength: 0.08, restLength: 4, dampingFactor: 0.85, maxSpeed: 0.6, centerForceStrength: 0.015, iterationsPerFrame: 2 },
          "🕸️ Web Analysis": { repulsionStrength: 40, attractionStrength: 0.12, restLength: 6, dampingFactor: 0.92, maxSpeed: 1.2, centerForceStrength: 0.008, iterationsPerFrame: 1 },
          "🔬 Detail Focus": { repulsionStrength: 80, attractionStrength: 0.25, restLength: 3, dampingFactor: 0.88, maxSpeed: 0.5, centerForceStrength: 0.025, iterationsPerFrame: 2 },
        };
        const presetHolder = { currentPreset: "🔍 Default View" };        layoutFolder.add(presetHolder, 'currentPreset', Object.keys(layoutPresets))
          .name('🎨 Analysis Preset')
          .onChange(presetName => {
            const selectedPreset = layoutPresets[presetName as keyof typeof layoutPresets];
            if (selectedPreset) {
              setLayoutOptions(selectedPreset); // This will trigger the useEffect below to update GUI
            }
          });        // Bind sliders to guiControlParamsRef.current with creative emojis
        // On change, update both the ref (implicitly by dat.gui) and the layoutOptions state
        layoutFolder.add(guiControlParamsRef.current, 'repulsionStrength', 10, 200).name('💥 Repulsion').onChange(val => setLayoutOptions(prev => ({...prev, repulsionStrength: val}))).listen();
        layoutFolder.add(guiControlParamsRef.current, 'attractionStrength', 0.01, 0.5, 0.01).name('🧲 Attraction').onChange(val => setLayoutOptions(prev => ({...prev, attractionStrength: val}))).listen();
        layoutFolder.add(guiControlParamsRef.current, 'restLength', 1, 20).name('📏 Rest Length').onChange(val => setLayoutOptions(prev => ({...prev, restLength: val}))).listen();
        layoutFolder.add(guiControlParamsRef.current, 'dampingFactor', 0.8, 0.99, 0.01).name('🌊 Damping').onChange(val => setLayoutOptions(prev => ({...prev, dampingFactor: val}))).listen();
        layoutFolder.add(guiControlParamsRef.current, 'maxSpeed', 0.1, 5, 0.1).name('🚀 Max Speed').onChange(val => setLayoutOptions(prev => ({...prev, maxSpeed: val}))).listen();
        layoutFolder.add(guiControlParamsRef.current, 'centerForceStrength', 0, 0.1, 0.001).name('🎯 Center Pull').onChange(val => setLayoutOptions(prev => ({...prev, centerForceStrength: val}))).listen();
        layoutFolder.add(guiControlParamsRef.current, 'iterationsPerFrame', 1, 10, 1).name('⚙️ Iterations/Frame').onChange(val => setLayoutOptions(prev => ({...prev, iterationsPerFrame: val}))).listen();        
        layoutFolder.add({ toggleLayout: () => setIsLayoutRunning(prev => !prev) }, 'toggleLayout').name(isLayoutRunning ? '⏸️ Pause Layout' : '▶️ Run Layout');
        
        layoutFolder.add({ reShuffleNodes: () => {
          nodes.forEach(node => {
            if (!node.isDragged && node.velocity) { 
              const shuffleStrength = layoutOptions.maxSpeed * 0.3;
              node.velocity.set(
                (Math.random() - 0.5) * shuffleStrength, 
                (Math.random() - 0.5) * shuffleStrength
              );
            }
          });
          if (!isLayoutRunning) { 
            setIsLayoutRunning(true); 
          }
        }}, 'reShuffleNodes').name('🎲 Re-Shuffle Nodes');
        
        layoutFolder.add({ energizeLayout: () => {        // Give all nodes a boost to escape local minima
        nodes.forEach(node => {
          if (!node.isDragged && node.velocity) {
            const energyBoost = layoutOptions.maxSpeed * 0.8;
            const angle = Math.random() * Math.PI * 2;
            node.velocity.add(new THREE.Vector2(
              Math.cos(angle) * energyBoost,
              Math.sin(angle) * energyBoost
            ));
          }
        });
        if (!isLayoutRunning) {
          setIsLayoutRunning(true);
        }
        }}, 'energizeLayout').name('⚡ Energize Layout');
        
        layoutFolder.open(); // Keep it open by default
    }

    const handleResize = () => {
      if (cameraRef.current && rendererRef.current && mountRef.current) {
        const aspect = mountRef.current.clientWidth / mountRef.current.clientHeight;
        const frustumSize = cameraRef.current.userData.frustumSize || 20;
        cameraRef.current.left = frustumSize * aspect / -2;
        cameraRef.current.right = frustumSize * aspect / 2;
        cameraRef.current.top = frustumSize / 2;
        cameraRef.current.bottom = frustumSize / -2;
        cameraRef.current.updateProjectionMatrix();
        rendererRef.current.setSize(mountRef.current.clientWidth, mountRef.current.clientHeight);
      }
    };
    // Store frustumSize for resize
    if (cameraRef.current) cameraRef.current.userData.frustumSize = 20; 
    window.addEventListener('resize', handleResize);
    
    // Convert mouse coordinates to world space for 2D
    const updateMouseWorldPosition = (event: MouseEvent) => {
      if (!mountRef.current || !cameraRef.current) return;
      const rect = mountRef.current.getBoundingClientRect();
      mouse.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
      mouse.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

      // Convert normalized device coordinates to world coordinates
      worldMouse.current.set(mouse.current.x, mouse.current.y, 0.5); // z can be anything between -1 and 1
      worldMouse.current.unproject(cameraRef.current);
      // For orthographic, we can ignore z or use a fixed plane.
      // The x and y are now in world units.
    };

    const onMouseMove = (event: MouseEvent) => {
      updateMouseWorldPosition(event);

      if (draggedNodeInfo && nodes.has(draggedNodeInfo.nodeId)) {
        const node = nodes.get(draggedNodeInfo.nodeId)!;
        node.position.x = worldMouse.current.x - draggedNodeInfo.offset.x;
        node.position.y = worldMouse.current.y - draggedNodeInfo.offset.y;
        node.mesh.position.set(node.position.x, node.position.y, 0);
        // If layout is running, consider making dragged node static or giving it high mass temporarily
        // For now, direct manipulation overrides layout for the dragged node.
        if (node.velocity) node.velocity.set(0,0); // Stop layout movement while dragging
        node.isDragged = true; // Explicitly mark as dragged
      } else {
        // Hover detection (no longer relies on physics bodies)
        if (!sceneRef.current || !cameraRef.current || nodes.size === 0) return;

        raycaster.current.setFromCamera(mouse.current, cameraRef.current);
        const intersects = raycaster.current.intersectObjects(
          Array.from(nodes.values()).map(n => n.mesh)
        );

        let newHoveredId: string | null = null;
        if (intersects.length > 0) {
          const firstIntersect = intersects[0].object;
          // Assuming node ID is stored in userData of the mesh
          if (firstIntersect.userData.nodeId) {
            newHoveredId = firstIntersect.userData.nodeId as string;
          }
        }        if (hoveredNodeId !== newHoveredId) {
          if (hoveredNodeId && nodes.has(hoveredNodeId)) {
            const prevNode = nodes.get(hoveredNodeId)!;
            // Only remove highlight if this node is not selected
            if (selectedNodeId !== hoveredNodeId) {
              prevNode.setHighlight(false);
              const material = prevNode.mesh.material as THREE.MeshBasicMaterial;
              material.userData.isHighlighted = false;
            }
          }
          if (newHoveredId && nodes.has(newHoveredId)) {
            const newNode = nodes.get(newHoveredId)!;
            newNode.setHighlight(true);
            const material = newNode.mesh.material as THREE.MeshBasicMaterial;
            material.userData.isHighlighted = true;
          }
          setHoveredNodeId(newHoveredId);
        }
      }
    };

    // Wrap onMouseMove and drag logic with throttle
    const throttledOnMouseMove = useRef<(e: MouseEvent) => void>();
    useEffect(() => {
      throttledOnMouseMove.current = throttleFn(onMouseMove, 16); // ~60fps max
    }, [onMouseMove]);

    const onMouseDown = (event: MouseEvent) => {
      if (event.button !== 0) return; // Only left click
      updateMouseWorldPosition(event); // Ensure worldMouse is up-to-date

      if (!sceneRef.current || !cameraRef.current || nodes.size === 0) return;

      raycaster.current.setFromCamera(mouse.current, cameraRef.current);
      const intersects = raycaster.current.intersectObjects(
        Array.from(nodes.values()).map(n => n.mesh)
      );

      if (intersects.length > 0) {
        const firstIntersect = intersects[0];
        const clickedNodeId = firstIntersect.object.userData.nodeId as string;
        
        if (clickedNodeId && nodes.has(clickedNodeId) && controlsRef.current) {
          controlsRef.current.enabled = false; // Disable camera pan/zoom while dragging

          const node = nodes.get(clickedNodeId)!;
          const offset = new THREE.Vector2(
            worldMouse.current.x - node.position.x,
            worldMouse.current.y - node.position.y
          );
          
          setDraggedNodeInfo({
            nodeId: clickedNodeId,
            offset: offset,
          });
          node.isDragged = true; // Mark as dragged
          if (node.velocity) node.velocity.set(0,0); // Stop movement from layout
        }
      }
    };    const onMouseUp = (event: MouseEvent) => {
      if (event.button !== 0) return; 
      if (controlsRef.current) {
        controlsRef.current.enabled = true; 
      }
      if (draggedNodeInfo) {
        const node = nodes.get(draggedNodeInfo.nodeId);
        if (node) {
          node.isDragged = false; // Release the drag state
        }
        setDraggedNodeInfo(null);
      }
    };    const onClick = (event: MouseEvent) => {
      if (event.button !== 0) return;

      // Update mouse coordinates for raycasting
      if (mountRef.current) {
        const rect = mountRef.current.getBoundingClientRect();
        mouse.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        // Store screen coordinates for info panel positioning
        const screenX = event.clientX - rect.left;
        const screenY = event.clientY - rect.top;

        // Perform raycasting
        raycaster.current.setFromCamera(mouse.current, cameraRef.current!);
        const nodeMeshes = Array.from(nodes.values()).map(node => node.mesh);
        const intersects = raycaster.current.intersectObjects(nodeMeshes);

        if (intersects.length > 0) {
          // Find the clicked node
          const clickedMesh = intersects[0].object;
          const clickedNode = Array.from(nodes.values()).find(node => node.mesh === clickedMesh);

          if (clickedNode) {
            // Use the enhanced node click handler for connection creation
            handleNodeClick(clickedNode, event);

            // If not in connection mode, handle normal selection
            if (!isCreatingConnection) {
              // Toggle selection
              if (selectedNodeId === clickedNode.id) {
                setSelectedNodeId(null);
                clickedNode.setHighlight(false);
                setShowInfoPanel(false);

                // Clear subgraph highlighting
                clearSubgraphHighlight(nodes, visualConnections);
              } else {
                // Clear previous selection
                if (selectedNodeId) {
                  const prevSelectedNode = nodes.get(selectedNodeId);
                  if (prevSelectedNode && selectedNodeId !== hoveredNodeId) {
                    prevSelectedNode.setHighlight(false);
                  }
                }

                // Clear any existing subgraph highlighting
                clearSubgraphHighlight(nodes, visualConnections);

                setSelectedNodeId(clickedNode.id);
                clickedNode.setHighlight(true);

                // Highlight connected subgraph
                highlightSubgraph(clickedNode.id, nodes, visualConnections, highlightOptions);

                // Show info panel
                setInfoPanelPosition({ x: screenX, y: screenY });
                setShowInfoPanel(true);
              }
            }
          }
        } else {
          // Clicked on empty space
          if (isCreatingConnection) {
            // Cancel connection creation
            handleCancelConnection();
          } else {
            // Clear selection
            if (selectedNodeId) {
              const prevSelectedNode = nodes.get(selectedNodeId);
              if (prevSelectedNode && selectedNodeId !== hoveredNodeId) {
                prevSelectedNode.setHighlight(false);
              }
              setSelectedNodeId(null);
              setShowInfoPanel(false);

              // Clear subgraph highlighting
              clearSubgraphHighlight(nodes, visualConnections);
            }
          }
        }
      }
    };

    // Right-click context menu handler
    const onContextMenu = (event: MouseEvent) => {
      event.preventDefault(); // Prevent default browser context menu

      if (!sceneRef.current || !cameraRef.current || nodes.size === 0) return;

      // Update mouse coordinates for raycasting
      if (mountRef.current) {
        const rect = mountRef.current.getBoundingClientRect();
        mouse.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouse.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        raycaster.current.setFromCamera(mouse.current, cameraRef.current);
        const intersects = raycaster.current.intersectObjects(
          Array.from(nodes.values()).map(n => n.mesh)
        );

        if (intersects.length > 0) {
          const firstIntersect = intersects[0];
          const clickedNodeId = firstIntersect.object.userData.nodeId as string;

          if (clickedNodeId && nodes.has(clickedNodeId)) {
            const node = nodes.get(clickedNodeId)!;

            // Show context menu for chat analysis nodes
            if (enableChatAnalysisIntegration && node.data.nodeType === 'analysis-record') {
              setContextMenuNode(node);
              setContextMenuPosition({
                x: event.clientX - rect.left,
                y: event.clientY - rect.top
              });
              setShowContextMenu(true);
            }
          }
        }
      }
    };

    // Click outside handler to close context menu
    const handleClickOutside = (event: MouseEvent) => {
      if (showContextMenu) {
        setShowContextMenu(false);
        setContextMenuNode(null);
      }
    };

    // Enhanced keyboard event handlers for cluster and simulation management
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent default for our shortcuts to avoid conflicts
      const isOurShortcut = (
        event.key === 'Escape' ||
        (event.key.toLowerCase() === 'c' && !event.ctrlKey && !event.metaKey && selectedNodes.size > 1) ||
        (event.key.toLowerCase() === 's' && !event.ctrlKey && !event.metaKey && selectedNodes.size > 0) ||
        (event.ctrlKey && event.shiftKey && event.key === '?') ||
        (event.ctrlKey && event.shiftKey && event.key === 'C') ||
        (event.key === 'F1')
      );

      if (isOurShortcut) {
        event.preventDefault();
      }

      // 'C' key to create cluster from selected nodes
      if (event.key.toLowerCase() === 'c' && !event.ctrlKey && !event.metaKey && selectedNodes.size > 1) {
        setHelpContext('clustering');
        handleShowClusterManager({ x: window.innerWidth / 2, y: window.innerHeight / 2 });
      }

      // 'S' key to create simulation from selected nodes
      if (event.key.toLowerCase() === 's' && !event.ctrlKey && !event.metaKey && selectedNodes.size > 0) {
        setHelpContext('simulations');
        handleShowSimulationManager({ x: window.innerWidth / 2, y: window.innerHeight / 2 });
      }

      // Escape key to clear selections or cancel operations
      if (event.key === 'Escape') {
        if (isCreatingConnection) {
          handleCancelConnection();
        } else if (selectedNodes.size > 0) {
          setSelectedNodes(new Set());
        } else {
          // Close any open panels
          setShowAdvancedControls(false);
          setShowClusterManager(false);
          setShowSimulationManager(false);
          setShowConnectionManager(false);
        }
      }

      // Ctrl+Shift+? to toggle keyboard shortcuts panel
      if (event.ctrlKey && event.shiftKey && event.key === '?') {
        toggleShortcuts();
      }

      // Ctrl+Shift+C to toggle advanced controls
      if (event.ctrlKey && event.shiftKey && event.key === 'C') {
        setShowAdvancedControls(!showAdvancedControls);
      }

      // F1 to toggle help system
      if (event.key === 'F1') {
        toggleHelp();
      }
    };

    useCanvasEventHandlers({
      mountRef,
      onMouseMove,
      onMouseDown,
      onMouseUp,
      onClick,
      onContextMenu,
      handleClickOutside,
      handleKeyDown
    });

    return () => {
      window.removeEventListener('resize', handleResize);      if (animationFrameIdRef.current) {
        cancelAnimationFrame(animationFrameIdRef.current);
      }
      mountRef.current?.removeChild(rendererRef.current!.domElement);
      controlsRef.current?.dispose();
      
      if (guiRef.current) {
        guiRef.current.destroy();
        guiRef.current = null;
      }      nodes.forEach(node => node.dispose());
      visualConnections.forEach(conn => conn.dispose());
      
      // Cleanup background effects
      if (particleFieldRef.current) {
        sceneRef.current?.remove(particleFieldRef.current);
        particleFieldRef.current.geometry.dispose();
        (particleFieldRef.current.material as THREE.PointsMaterial).dispose();
        particleFieldRef.current = null;
      }
      if (backgroundMeshRef.current) {
        sceneRef.current?.remove(backgroundMeshRef.current);
        backgroundMeshRef.current.geometry.dispose();
        (backgroundMeshRef.current.material as THREE.ShaderMaterial).dispose();
        backgroundMeshRef.current = null;
      }    };
  }, []); // Only run once on mount

  // Effect to update dat.gui controllers when layoutOptions state changes (e.g., from presets)
  useEffect(() => {
    // Update the ref that dat.gui is bound to
    guiControlParamsRef.current.repulsionStrength = layoutOptions.repulsionStrength;
    guiControlParamsRef.current.attractionStrength = layoutOptions.attractionStrength;
    guiControlParamsRef.current.restLength = layoutOptions.restLength;
    guiControlParamsRef.current.dampingFactor = layoutOptions.dampingFactor;
    guiControlParamsRef.current.maxSpeed = layoutOptions.maxSpeed;
    guiControlParamsRef.current.centerForceStrength = layoutOptions.centerForceStrength;
    guiControlParamsRef.current.iterationsPerFrame = layoutOptions.iterationsPerFrame || 1;

    // Tell dat.gui to refresh the display of its controllers in the layout folder
    if (guiRef.current) {
      const layoutFolder = guiRef.current.__folders['Layout Controls'];
      if (layoutFolder) {
        layoutFolder.__controllers.forEach(controller => {
          // Check if the controller's property exists in guiControlParamsRef.current
          // to avoid errors if other controllers are in the same folder.
          // And ensure it's not the preset dropdown itself.
          if (guiControlParamsRef.current.hasOwnProperty(controller.property) && controller.property !== 'currentPreset') {
            controller.updateDisplay();
          }
        });
      }
    }
  }, [layoutOptions]); // Run when layoutOptions (from React state) changes
  // Update GUI play/pause button text when isLayoutRunning changes
  useEffect(() => {
    if (guiRef.current) {
      const layoutFolder = guiRef.current.__folders['Layout Controls'];
      if (layoutFolder) {
        const controller = layoutFolder.__controllers.find(c => c.property === 'toggleLayout');        if (controller) {
          controller.name(isLayoutRunning ? '⏸️ Pause Layout' : '▶️ Run Layout');
        }
      }
    }
  }, [isLayoutRunning]);
  // Enhanced node creation with creative visual styling
  useEffect(() => {
    // Determine which data to use: chat analysis data or initial data
    const dataToUse = enableChatAnalysisIntegration && chatCanvasData ? chatCanvasData : initialData;

    if (dataToUse && sceneRef.current) {
      const newNodesMap = new Map<string, SimpleNode>();
      originalNodePositionsRef.current.clear();

      // Calculate node degrees for dynamic sizing
      const nodeDegrees = new Map<string, number>();
      dataToUse.nodes.forEach(node => nodeDegrees.set(node.id, 0));
      dataToUse.connections.forEach(conn => {
        nodeDegrees.set(conn.source, (nodeDegrees.get(conn.source) || 0) + 1);
        nodeDegrees.set(conn.target, (nodeDegrees.get(conn.target) || 0) + 1);
      });      // Enhanced category color mapping with modern, accessible color palette
      const categoryColors = {
        'core': 0xef4444,      // Modern red (Tailwind red-500)
        'input': 0x10b981,     // Modern emerald (Tailwind emerald-500)
        'process': 0x3b82f6,   // Modern blue (Tailwind blue-500)
        'output': 0x8b5cf6,    // Modern violet (Tailwind violet-500)
        'interface': 0xf59e0b, // Modern amber (Tailwind amber-500)
        'storage': 0x06b6d4,   // Modern cyan (Tailwind cyan-500)
        'default': 0x6366f1    // Modern indigo (Tailwind indigo-500)
      };

      dataToUse.nodes.forEach(nodeData => {
        // Dynamic sizing based on value and degree
        const nodeValue = nodeData.value || 50;
        const degree = nodeDegrees.get(nodeData.id) || 0;
        const baseSize = 0.3;
        const valueSize = (nodeValue / 100) * 0.4; // Scale by value
        const degreeSize = degree * 0.1; // Add size based on connections
        const finalSize = Math.max(baseSize, baseSize + valueSize + degreeSize);

        // Dynamic coloring based on category or use specified color
        const categoryColor = nodeData.category ? categoryColors[nodeData.category] || categoryColors.default : null;
        const finalColor = categoryColor || nodeData.color || new THREE.Color(Math.random() * 0xffffff);        // Enhanced geometry with modern material properties
        const geometry = new THREE.CircleGeometry(finalSize, 64); // Increased segments for smoother circles
        const material = new THREE.MeshBasicMaterial({ 
          color: finalColor,
          transparent: true,
          opacity: 0.9
        });
        const circleMesh = new THREE.Mesh(geometry, material);
        
        // Enhanced outline ring with gradient-like effect
        const ringGeometry = new THREE.RingGeometry(finalSize * 0.92, finalSize * 1.12, 64);
        const ringMaterial = new THREE.MeshBasicMaterial({ 
          color: new THREE.Color(finalColor).lerp(new THREE.Color(0xffffff), 0.3), // Lighter tint
          transparent: true,
          opacity: 0.4,
          side: THREE.DoubleSide
        });
        const ringMesh = new THREE.Mesh(ringGeometry, ringMaterial);
        
        // Add inner glow effect
        const glowGeometry = new THREE.CircleGeometry(finalSize * 1.3, 32);
        const glowMaterial = new THREE.MeshBasicMaterial({
          color: finalColor,
          transparent: true,
          opacity: 0.1,
          blending: THREE.AdditiveBlending // Creates glow effect
        });
        const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial);
        
        circleMesh.add(ringMesh); // Add ring as child of main mesh
        circleMesh.add(glowMesh); // Add glow effect
        
        const position = nodeData.position 
          ? new THREE.Vector2(nodeData.position.x, nodeData.position.y)
          : new THREE.Vector2((Math.random() - 0.5) * 10, (Math.random() - 0.5) * 10);
        
        circleMesh.position.set(position.x, position.y, 0);
        circleMesh.userData.nodeId = nodeData.id;
        circleMesh.userData.originalColor = finalColor;
        circleMesh.userData.originalOpacity = 0.9;

        sceneRef.current!.add(circleMesh);

        const simpleNode: SimpleNode = {
          id: nodeData.id,
          mesh: circleMesh,
          position: position.clone(),
          data: nodeData,
          velocity: new THREE.Vector2(),
          force: new THREE.Vector2(),
          isDragged: false,          setHighlight: (highlight) => {
            const mainMaterial = circleMesh.material as THREE.MeshBasicMaterial;
            const ringMaterial = ringMesh.material as THREE.MeshBasicMaterial;
            const glowMaterial = glowMesh.material as THREE.MeshBasicMaterial;
            
            if (highlight) {
              // Enhanced hover effects with modern styling
              mainMaterial.color.set(0xfbbf24); // Warm amber highlight
              mainMaterial.opacity = 1.0;
              ringMaterial.opacity = 0.8;
              ringMaterial.color.set(0xfcd34d); // Lighter amber for ring
              glowMaterial.opacity = 0.3; // Increase glow
              glowMaterial.color.set(0xfbbf24);
              
              // Smooth scale animation for highlight
              circleMesh.scale.setScalar(1.5);
              ringMesh.scale.setScalar(1.4);
              glowMesh.scale.setScalar(1.2);
              
              mainMaterial.userData.isHighlighted = true;
            } else {
              // Restore original appearance with smooth transitions
              mainMaterial.color.set(circleMesh.userData.originalColor);
              mainMaterial.opacity = circleMesh.userData.originalOpacity;
              ringMaterial.opacity = 0.4;
              ringMaterial.color.set(new THREE.Color(circleMesh.userData.originalColor).lerp(new THREE.Color(0xffffff), 0.3));
              glowMaterial.opacity = 0.1;
              glowMaterial.color.set(circleMesh.userData.originalColor);
              
              circleMesh.scale.setScalar(1.0);
              ringMesh.scale.setScalar(1.0);
              glowMesh.scale.setScalar(1.0);
              
              mainMaterial.userData.isHighlighted = false;
            }
          },          dispose: () => {
            sceneRef.current?.remove(circleMesh);
            geometry.dispose();
            material.dispose();
            ringGeometry.dispose();
            ringMaterial.dispose();
            glowGeometry.dispose();
            glowMaterial.dispose();
          }
        };
        newNodesMap.set(nodeData.id, simpleNode);
        originalNodePositionsRef.current.set(nodeData.id, position.clone());
      });
      setNodes(newNodesMap);

      // Connections setup (remains largely the same)
      const newConnectionsMap = new Map<string, VisualConnection>();
      dataToUse.connections.forEach(connData => {
        const sourceNode = newNodesMap.get(connData.source);
        const targetNode = newNodesMap.get(connData.target);        if (sourceNode && targetNode && sceneRef.current && mountRef.current) {          // Enhanced edge styling with improved visual appeal
          const strength = connData.strength || 1.0;
          const edgeColor = getEdgeColorByStrength(strength);
          
          // Modern line material with better visual properties
          const lineMaterial = new THREE.LineBasicMaterial({
            color: edgeColor,
            opacity: 0.6 + (strength * 0.3), // Dynamic opacity based on strength
            transparent: true,
            linewidth: Math.max(1, strength * 2), // Dynamic width (limited browser support)
          });
          
          // Create curved line geometry using edge utilities
          const start = new THREE.Vector3(
            sourceNode.mesh.position.x, 
            sourceNode.mesh.position.y, 
            sourceNode.mesh.position.z
          );
          const end = new THREE.Vector3(
            targetNode.mesh.position.x, 
            targetNode.mesh.position.y, 
            targetNode.mesh.position.z
          );
            // Enhanced curved geometry for more appealing edges
          const curvedGeometry = createCurvedLineGeometry(start, end, 0.2, 32); // Increased curve and segments
          const line = new THREE.Line(curvedGeometry, lineMaterial);
          sceneRef.current.add(line);

          const visualConn: VisualConnection = {
            id: connData.id,
            lineMesh: line,
            sourceNode: sourceNode, 
            targetNode: targetNode, 
            update: () => {
              // Update curved line with new positions
              const newStart = new THREE.Vector3(
                sourceNode.mesh.position.x, 
                sourceNode.mesh.position.y, 
                sourceNode.mesh.position.z
              );
              const newEnd = new THREE.Vector3(
                targetNode.mesh.position.x, 
                targetNode.mesh.position.y, 
                targetNode.mesh.position.z
              );
              
              const newCurvedGeometry = createCurvedLineGeometry(newStart, newEnd, 0.2, 32);
              line.geometry.dispose(); // Clean up old geometry
              line.geometry = newCurvedGeometry;
            },
            dispose: () => {
              sceneRef.current!.remove(line);
              line.geometry.dispose();
              lineMaterial.dispose();
            }
          };
          newConnectionsMap.set(connData.id, visualConn);
        }
      });
      setVisualConnections(newConnectionsMap);
    }
  }, [initialData, enableChatAnalysisIntegration, chatCanvasData]); // Updated dependencies

  const resetNodePositions = () => {
    nodes.forEach(node => {
      const originalPos = originalNodePositionsRef.current.get(node.id);
      if (originalPos) {
        node.position.copy(originalPos);
        node.mesh.position.set(originalPos.x, originalPos.y, 0);
        if (node.velocity) node.velocity.set(0,0); // Reset velocity
      }
    });    // May need to re-initialize/stabilize layout after reset
    // setIsLayoutRunning(false); // Optionally pause layout
    // setTimeout(() => setIsLayoutRunning(true), 100); // And restart after a tick
  };

  // Search and filter handlers (commented out for now)
  const handleSearchResult = useCallback((nodeIds: string[]) => {
    // setSearchResults(nodeIds);
    // Enhanced search and filter result highlighting
    nodes.forEach((node, nodeId) => {
      const material = node.mesh.material as THREE.MeshBasicMaterial;
      if (nodeIds.length === 0) {
        // No search results - restore normal appearance with smooth transition
        material.opacity = node.mesh.userData.originalOpacity || 0.9;
        node.mesh.scale.setScalar(1.0);
        // Restore original color
        material.color.set(node.mesh.userData.originalColor);
      } else if (nodeIds.includes(nodeId)) {
        // Highlight search results with pulsing effect
        material.opacity = 1.0;
        node.mesh.scale.setScalar(1.3);
        // Use warm highlight color        material.color.set(0xfbbf24);
      } else {
        // Dim non-matching nodes
        material.opacity = 0.2;
        node.mesh.scale.setScalar(0.7);
        // Desaturate color
        const originalColor = new THREE.Color(node.mesh.userData.originalColor);
        material.color.copy(originalColor.lerp(new THREE.Color(0x64748b), 0.7));
      }
    });
  }, [nodes.size]); // Use nodes.size instead of nodes Map for better performance

  const handleFilterApply = useCallback((nodeIds: string[]) => {
    // setFilteredNodes(nodeIds);
    // Enhanced filter visualization with better visual feedback
    nodes.forEach((node, nodeId) => {
      const material = node.mesh.material as THREE.MeshBasicMaterial;
      if (nodeIds.includes(nodeId)) {
        // Show filtered nodes with enhanced appearance
        material.opacity = node.mesh.userData.originalOpacity || 0.9;
        node.mesh.scale.setScalar(1.1);
        // Slight color enhancement for filtered nodes
        const originalColor = new THREE.Color(node.mesh.userData.originalColor);
        material.color.copy(originalColor.lerp(new THREE.Color(0xffffff), 0.1));
      } else {
        // Hide or heavily dim filtered out nodes
        material.opacity = 0.05;
        node.mesh.scale.setScalar(0.4);
        // Heavy desaturation
        const originalColor = new THREE.Color(node.mesh.userData.originalColor);        material.color.copy(originalColor.lerp(new THREE.Color(0x1e293b), 0.8));
      }
    });
  }, [nodes.size]); // Use nodes.size instead of nodes Map for better performance

  const handleClearFilters = useCallback(() => {
    // setSearchResults([]);
    // setFilteredNodes([]);
    // Restore all nodes to enhanced normal appearance
    nodes.forEach((node) => {
      const material = node.mesh.material as THREE.MeshBasicMaterial;
      material.opacity = node.mesh.userData.originalOpacity || 0.9;
      node.mesh.scale.setScalar(1.0);
      // Restore original colors
      material.color.set(node.mesh.userData.originalColor);
    });
  }, [nodes.size]); // Use nodes.size instead of nodes Map for better performance

  // Contextual menu action handler
  const handleContextMenuAction = useCallback(async (action: ChatAnalysisNodeMenuAction, nodeId: string, analysisRecord?: AnalysisResult) => {
    console.log(`Executing action: ${action} on node: ${nodeId}`, analysisRecord);

    switch (action) {
      case 'view-details':
        // Open detailed view of the analysis record
        if (analysisRecord) {
          // For now, just show an alert - this would open a detailed modal
          alert(`Viewing details for: ${analysisRecord.question}`);
        }
        break;

      case 're-run-analysis':
        // Re-run the analysis with the same parameters
        if (analysisRecord) {
          alert(`Re-running analysis: ${analysisRecord.question}`);
          // TODO: Implement actual re-run logic
        }
        break;

      case 'modify-parameters':
        // Open parameter modification dialog
        if (analysisRecord) {
          alert(`Modifying parameters for: ${analysisRecord.question}`);
          // TODO: Open parameter modification modal
        }
        break;

      case 'duplicate-analysis':
        // Create a copy of the analysis
        if (analysisRecord) {
          alert(`Duplicating analysis: ${analysisRecord.question}`);
          // TODO: Implement duplication logic
        }
        break;

      case 'initiate-simulation':
        // Start a chat simulation using this analysis with LangChain-generated prompts
        if (analysisRecord) {
          try {
            // Generate simulation prompts using LangChain
            const prompts = await generateSimulationPrompts([analysisRecord], 'exploration');
            console.log('Generated simulation prompts:', prompts);

            // Set the node as selected and show simulation manager
            setSelectedNodes(new Set([nodeId]));
            handleShowSimulationManager({ x: window.innerWidth / 2, y: window.innerHeight / 2 });
          } catch (error) {
            console.error('Failed to generate simulation prompts:', error);
            // Fallback to manual simulation creation
            setSelectedNodes(new Set([nodeId]));
            handleShowSimulationManager({ x: window.innerWidth / 2, y: window.innerHeight / 2 });
          }
        }
        break;

      case 'add-to-cluster':
        // Add node to a cluster
        alert(`Adding node ${nodeId} to cluster`);
        // TODO: Show cluster selection dialog
        break;

      case 'remove-from-cluster':
        // Remove node from its current cluster
        alert(`Removing node ${nodeId} from cluster`);
        // TODO: Implement cluster removal
        break;

      case 'toggle-favorite':
        // Toggle favorite status
        if (analysisRecord) {
          alert(`Toggling favorite for: ${analysisRecord.question}`);
          // TODO: Update favorite status
        }
        break;

      case 'archive':
        // Archive the analysis record
        alert(`Archiving node ${nodeId}`);
        // TODO: Implement archiving
        break;

      case 'delete':
        // Delete the analysis record
        if (confirm(`Are you sure you want to delete this analysis?`)) {
          alert(`Deleting node ${nodeId}`);
          // TODO: Implement deletion
        }
        break;

      default:
        console.warn(`Unknown action: ${action}`);
    }
  }, [generateSimulationPrompts]);

  // Connection creation handlers
  const handleCreateConnection = useCallback((connectionData: ConnectionData) => {
    console.log('Creating connection:', connectionData);

    // Add the connection to the visual connections map
    if (connectionSourceNode && connectionTargetNode && sceneRef.current) {
      const strength = connectionData.strength || 1.0;
      const edgeColor = getEdgeColorByStrength(strength);

      // Create curved line geometry (convert Vector2 to Vector3)
      const sourcePos = new THREE.Vector3(connectionSourceNode.position.x, connectionSourceNode.position.y, 0);
      const targetPos = new THREE.Vector3(connectionTargetNode.position.x, connectionTargetNode.position.y, 0);
      const curveGeometry = createCurvedLineGeometry(
        sourcePos,
        targetPos,
        0.3 // curve amount
      );

      const lineMaterial = new THREE.LineBasicMaterial({
        color: edgeColor,
        linewidth: Math.max(1, strength * 2),
        transparent: true,
        opacity: 0.8
      });

      const lineMesh = new THREE.Line(curveGeometry, lineMaterial);
      sceneRef.current.add(lineMesh);

      const visualConn: VisualConnection = {
        id: connectionData.id,
        lineMesh,
        sourceNode: connectionSourceNode,
        targetNode: connectionTargetNode,
        update: () => {
          // Update line geometry when nodes move (convert Vector2 to Vector3)
          const sourcePos = new THREE.Vector3(connectionSourceNode.position.x, connectionSourceNode.position.y, 0);
          const targetPos = new THREE.Vector3(connectionTargetNode.position.x, connectionTargetNode.position.y, 0);
          const newGeometry = createCurvedLineGeometry(
            sourcePos,
            targetPos,
            0.3
          );
          lineMesh.geometry.dispose();
          lineMesh.geometry = newGeometry;
        },
        dispose: () => {
          sceneRef.current?.remove(lineMesh);
          lineMesh.geometry.dispose();
          lineMaterial.dispose();
        }
      };

      setVisualConnections(prev => new Map(prev).set(connectionData.id, visualConn));
    }

    // Close connection manager
    setShowConnectionManager(false);
    setConnectionSourceNode(null);
    setConnectionTargetNode(null);
    setIsCreatingConnection(false);
  }, [connectionSourceNode, connectionTargetNode]);

  const handleCancelConnection = useCallback(() => {
    setShowConnectionManager(false);
    setConnectionSourceNode(null);
    setConnectionTargetNode(null);
    setIsCreatingConnection(false);
  }, []);

  // Cluster management handlers
  const { clusters, createCluster, updateCluster, deleteCluster } = chatAnalysisCanvas;

  const handleCreateCluster = useCallback((name: string, description: string, nodeIds: string[], color?: number) => {
    createCluster(nodeIds, name, description);
    setShowClusterManager(false);
    setSelectedNodes(new Set());
  }, [createCluster]);

  const handleUpdateCluster = useCallback((clusterId: string, updates: Record<string, unknown>) => {
    updateCluster(clusterId, updates);
  }, [updateCluster]);

  const handleDeleteCluster = useCallback((clusterId: string) => {
    deleteCluster(clusterId);
    setEditingCluster(null);
  }, [deleteCluster]);

  const handleNodeSelection = useCallback((nodeId: string, isSelected: boolean, multiSelect: boolean = false) => {
    setSelectedNodes(prev => {
      const newSelection = new Set(multiSelect ? prev : []);
      if (isSelected) {
        newSelection.add(nodeId);
      } else {
        newSelection.delete(nodeId);
      }
      return newSelection;
    });
  }, []);

  const handleShowClusterManager = useCallback((position: { x: number; y: number }) => {
    setClusterManagerPosition(position);
    setShowClusterManager(true);
  }, []);

  // Simulation management handlers
  const { simulations, createSimulation, updateSimulation } = chatAnalysisCanvas;

  const handleCreateSimulation = useCallback((name: string, description: string, sourceNodeIds: string[], prompts: unknown[]) => {
    createSimulation(name, description, sourceNodeIds, prompts);
    setShowSimulationManager(false);
  }, [createSimulation]);

  const handleUpdateSimulation = useCallback((simulationId: string, updates: Record<string, unknown>) => {
    updateSimulation(simulationId, updates);
  }, [updateSimulation]);

  const handleShowSimulationManager = useCallback((position: { x: number; y: number }) => {
    setSimulationManagerPosition(position);
    setShowSimulationManager(true);
  }, []);

  const handleRunSimulation = useCallback((simulationId: string) => {
    setRunningSimulation(simulationId);
    setShowSimulationRunner(true);
  }, []);

  // Advanced control handlers
  const handleLayoutChange = useCallback((algorithm: string, parameters: Record<string, unknown>) => {
    console.log('Layout change:', algorithm, parameters);
    // TODO: Implement layout algorithm switching
  }, []);

  const handleVisibilityToggle = useCallback((element: string, visible: boolean) => {
    setVisibilitySettings(prev => ({
      ...prev,
      [element]: visible
    }));
  }, []);

  const handleThemeChange = useCallback((theme: string) => {
    setCurrentTheme(theme);
    // TODO: Apply theme changes to canvas
  }, []);

  const handleExport = useCallback((format: string) => {
    console.log('Exporting in format:', format);
    // TODO: Implement export functionality
  }, []);

  const handleImport = useCallback((file: File) => {
    console.log('Importing file:', file.name);
    // TODO: Implement import functionality
  }, []);

  const handleReset = useCallback(() => {
    if (confirm('Are you sure you want to reset the canvas to default settings?')) {
      // TODO: Implement reset functionality
      console.log('Resetting canvas');
    }
  }, []);

  // Enhanced control handlers
  const handleAnimationToggle = useCallback((enabled: boolean) => {
    console.log('Animation toggle:', enabled);
    // TODO: Implement animation control
  }, []);

  const handleSoundToggle = useCallback((enabled: boolean) => {
    console.log('Sound toggle:', enabled);
    // TODO: Implement sound control
  }, []);

  const handleAccessibilityChange = useCallback((setting: string, value: unknown) => {
    console.log('Accessibility change:', setting, value);
    // TODO: Implement accessibility settings
  }, []);

  const handleViewportChange = useCallback((viewport: string) => {
    console.log('Viewport change:', viewport);
    // TODO: Implement viewport switching
  }, []);

  const handleZoomChange = useCallback((zoom: number) => {
    console.log('Zoom change:', zoom);
    // TODO: Implement zoom control
  }, []);

  const handleFilterChange = useCallback((filters: Record<string, unknown>) => {
    console.log('Filter change:', filters);
    // TODO: Implement filtering
  }, []);

  const handleBookmark = useCallback(() => {
    console.log('Bookmark current view');
    // TODO: Implement bookmarking
  }, []);

  const handleShare = useCallback(() => {
    console.log('Share canvas');
    // TODO: Implement sharing
  }, []);

  // Enhanced click handler to support connection creation and multi-selection
  const handleNodeClick = useCallback((clickedNode: SimpleNode, event: MouseEvent) => {
    if (isCreatingConnection && connectionSourceNode) {
      // Second click - set target and show connection manager
      if (clickedNode.id !== connectionSourceNode.id) {
        setConnectionTargetNode(clickedNode);

        // Position the connection manager
        if (mountRef.current) {
          const rect = mountRef.current.getBoundingClientRect();
          setConnectionManagerPosition({
            x: event.clientX - rect.left,
            y: event.clientY - rect.top
          });
        }

        setShowConnectionManager(true);
      } else {
        // Clicked same node - cancel connection creation
        handleCancelConnection();
      }
    } else if (event.ctrlKey || event.metaKey) {
      // Ctrl/Cmd + click for multi-selection or connection creation
      if (event.shiftKey) {
        // Ctrl+Shift+click to start connection creation
        setConnectionSourceNode(clickedNode);
        setIsCreatingConnection(true);
        console.log('Connection mode started. Click another node to connect.');
      } else {
        // Ctrl+click for multi-selection
        const isCurrentlySelected = selectedNodes.has(clickedNode.id);
        handleNodeSelection(clickedNode.id, !isCurrentlySelected, true);

        // Visual feedback for selection
        clickedNode.setHighlight(!isCurrentlySelected);
      }
    } else {
      // Normal click behavior - clear multi-selection and handle normal selection
      if (selectedNodes.size > 0) {
        // Clear multi-selection visual feedback
        selectedNodes.forEach(nodeId => {
          const node = nodes.get(nodeId);
          if (node && nodeId !== clickedNode.id) {
            node.setHighlight(false);
          }
        });
        setSelectedNodes(new Set());
      }

      // Normal single selection logic will be handled in the main onClick function
    }
  }, [isCreatingConnection, connectionSourceNode, handleCancelConnection, selectedNodes, handleNodeSelection, nodes]);

  // Initialize cluster visualization
  useEffect(() => {
    if (sceneRef.current && !clusterVisualizationRef.current) {
      clusterVisualizationRef.current = new ClusterVisualization(sceneRef.current);
    }
  }, []);

  // Update cluster visualizations when clusters change
  useEffect(() => {
    if (clusterVisualizationRef.current && clusters) {
      clusterVisualizationRef.current.updateAllClusters(clusters, nodes);
    }  }, [clusters, nodes]);

  return (
    <div style={{ 
      position: 'relative', 
      width: '100%', 
      height: 'calc(100vh - 70px)',
      background: 'linear-gradient(135deg, #0a0a1e 0%, #1a1a2e 50%, #16213e 100%)', // Enhanced gradient background
      borderRadius: '12px',
      overflow: 'hidden',
      boxShadow: '0 20px 40px rgba(0, 0, 0, 0.3), inset 0 1px 0 rgba(255, 255,  255, 0.1)', // Modern glass effect
    }}>
      <div
        ref={mountRef}
        style={{
          width: '100%',
          height: '100%',
          border: isCreatingConnection
            ? '2px solid rgba(34, 197, 94,  0.6)' // Green border when creating connections
            : '1px solid rgba(99, 102, 241, 0.3)', // Subtle colored border
          borderRadius: '12px',
          touchAction: 'none',
          background: 'transparent',
          cursor: isCreatingConnection ? 'crosshair' : 'default',
        }}
      />

      {/* Connection Creation Status */}
      {isCreatingConnection && connectionSourceNode && (
       
        <div style={{
          position: 'absolute',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',

          background: 'rgba(34, 197, 94, 0.9)',
          color: 'white',
          padding: '8px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          zIndex: 10,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
        }}>
          🔗 Click another node to create connection from "{connectionSourceNode.data.label || connectionSourceNode.id}"
        </div>
      )}

      {/* Multi-Selection Status */}
      {selectedNodes.size > 0 && !isCreatingConnection && (
        <div style={{
          position: 'absolute',
          top: '20px',
          left: '50%',
          transform: 'translateX(-50%)',
          background: 'rgba(59, 130, 246, 0.9)',
          color: 'white',
          padding: '8px 16px',
          borderRadius: '8px',
          fontSize: '14px',
          fontWeight: '500',
          zIndex: 10,
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.3)',
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
        }}>
          👥 {selectedNodes.size} nodes selected
          <div style={{ display: 'flex', gap: '4px' }}>
            {selectedNodes.size > 1 && (
              <span style={{
                background: 'rgba(255, 255, 255, 0.2)',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '12px',
              }}>
                Press 'C' for cluster
              </span>
            )}
            {selectedNodes.size > 0 && (
              <span style={{
                background: 'rgba(255, 255, 255, 0.2)',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '12px',
              }}>
                Press 'S' for simulation
              </span>
            )}
          </div>
        </div>
      )}
      
      {/* Filter and Search Panel */}
      <FilterSearchPanel
        nodes={nodes}
        connections={visualConnections}
        onSearchResult={handleSearchResult}
        onFilterApply={handleFilterApply}
        onClearFilters={handleClearFilters}
        isVisible={showFilterPanel}
        onToggle={() => setShowFilterPanel(!showFilterPanel)}
      />
      
      {showInfoPanel && selectedNodeId && (
        <ContextualInfoPanel
          selectedNode={nodes.get(selectedNodeId) || null}
          selectedEdge={null}
          position={infoPanelPosition}          onClose={() => {
            setShowInfoPanel(false);
            if (selectedNodeId) {
              const selectedNode = nodes.get(selectedNodeId);
              if (selectedNode && selectedNodeId !== hoveredNodeId) {
                selectedNode.setHighlight(false);
              }
              setSelectedNodeId(null);
              
              // Clear subgraph highlighting
              clearSubgraphHighlight(nodes, visualConnections);
            }
          }}
        />
      )}

      {/* Chat Analysis Contextual Menu */}
      {showContextMenu && contextMenuNode && enableChatAnalysisIntegration && (
        <ChatAnalysisNodeMenu
          node={contextMenuNode}
          position={contextMenuPosition}
          onClose={() => {
            setShowContextMenu(false);
            setContextMenuNode(null);
          }}
          onAction={handleContextMenuAction}
        />
      )}

      {/* Node Connection Manager */}
      {showConnectionManager && connectionSourceNode && connectionTargetNode && (
        <NodeConnectionManager
          sourceNode={connectionSourceNode}
          targetNode={connectionTargetNode}
          position={connectionManagerPosition}
          onSave={handleCreateConnection}
          onCancel={handleCancelConnection}
        />
      )}

      {/* Cluster Manager */}
      {showClusterManager && (
        <ClusterManager
          clusters={clusters || []}
          selectedNodes={Array.from(selectedNodes).map(id => nodes.get(id)!).filter(Boolean)}
          position={clusterManagerPosition}
          onCreateCluster={handleCreateCluster}
          onUpdateCluster={handleUpdateCluster}
          onDeleteCluster={handleDeleteCluster}
          onClose={() => setShowClusterManager(false)}
          existingCluster={editingCluster ? clusters?.find(c => c.id === editingCluster) : undefined}
        />
      )}

      {/* Chat Simulation Manager */}
      {showSimulationManager && (
        <ChatSimulationManager
          selectedNodes={Array.from(selectedNodes).map(id => nodes.get(id)!).filter(Boolean)}
          position={simulationManagerPosition}
          onCreateSimulation={handleCreateSimulation}
          onClose={() => setShowSimulationManager(false)}
        />
      )}

      {/* Simulation Runner */}
      {showSimulationRunner && runningSimulation && simulations && (
        <SimulationRunner
          simulation={simulations.find(s => s.id === runningSimulation)!}
          position={{ x: window.innerWidth / 2 - 340, y: window.innerHeight / 2 - 290 }}
          onUpdateSimulation={handleUpdateSimulation}
          onClose={() => {
            setShowSimulationRunner(false);
            setRunningSimulation(null);
          }}
        />
      )}

      {/* Real-time Connection Status */}
      {enableChatAnalysisIntegration && (
        <StatusTooltip
          status={connectionStatus === 'connected' ? 'success' : connectionStatus === 'error' ? 'error' : 'warning'}
          title="Real-time Connection"
          description={`WebSocket connection status: ${connectionStatus}. ${isConnected ? 'Receiving live updates.' : 'No live updates available.'}`}
        >
          <div style={{
            position: 'absolute',
            bottom: '20px',
            right: '20px',
            background: 'rgba(20, 20, 40, 0.9)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.2)',
            borderRadius: '8px',
            padding: '8px 12px',
            fontSize: '12px',
            color: 'white',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
            zIndex: 5,
            cursor: 'help'
          }}>
            <div
              style={{
                width: '8px',
                height: '8px',
                borderRadius: '50%',
                backgroundColor:
                  connectionStatus === 'connected' ? '#10b981' :
                  connectionStatus === 'connecting' || connectionStatus === 'reconnecting' ? '#f59e0b' :
                  '#ef4444'
              }}
            />
            <span>
              {connectionStatus === 'connected' ? 'Live' :
               connectionStatus === 'connecting' ? 'Connecting...' :
               connectionStatus === 'reconnecting' ? 'Reconnecting...' :
               connectionStatus === 'error' ? 'Connection Error' :
               'Offline'}
            </span>
            {isConnected && liveUpdates.analysisResults.length > 0 && (
              <span style={{
                background: 'rgba(34, 197, 94, 0.2)',
                padding: '2px 6px',
                borderRadius: '4px',
                fontSize: '10px'
              }}>
                +{liveUpdates.analysisResults.length}
              </span>
            )}
          </div>
        </StatusTooltip>
      )}

      {/* Performance Metrics (Debug Mode) */}
      {enableChatAnalysisIntegration && performanceMetrics.fps < 45 && (
        <div style={{
          position: 'absolute',
          bottom: '70px',
          right: '20px',
          background: 'rgba(239, 68, 68, 0.9)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '8px',
          padding: '8px 12px',
          fontSize: '11px',
          color: 'white',
          zIndex: 5,
        }}>
          <div>⚠️ Performance Warning</div>
          <div>FPS: {performanceMetrics.fps}</div>
          <div>Frame: {performanceMetrics.frameTime}ms</div>
          <div>Memory: {performanceMetrics.memoryUsage}MB</div>
        </div>
      )}

      {/* Advanced Control Panel */}
      <AdvancedControlPanel
        isVisible={showAdvancedControls}
        onToggle={() => setShowAdvancedControls(!showAdvancedControls)}
        onLayoutChange={handleLayoutChange}
        onVisibilityToggle={handleVisibilityToggle}
        onThemeChange={handleThemeChange}
        onExport={handleExport}
        onImport={handleImport}
        onReset={handleReset}
        onAnimationToggle={handleAnimationToggle}
        onSoundToggle={handleSoundToggle}
        onAccessibilityChange={handleAccessibilityChange}
        onViewportChange={handleViewportChange}
        onZoomChange={handleZoomChange}
        onFilterChange={handleFilterChange}
        onBookmark={handleBookmark}
        onShare={handleShare}
        currentSettings={{
          layout: { algorithm: 'force-directed', parameters: {} },
          visibility: visibilitySettings,
          theme: currentTheme,
          performance: {
            fps: performanceMetrics.fps,
            nodeCount: nodes.size,
            connectionCount: visualConnections.size
          },
          animation: { enabled: true, speed: 1 },
          sound: { enabled: false, volume: 0.5 },
          accessibility: {
            highContrast: false,
            fontSize: 14,
            reducedMotion: false
          },
          viewport: 'desktop',
          zoom: 1,
          filters: {}
        }}
      />

      {/* Keyboard Shortcuts Manager */}
      <KeyboardShortcutsManager
        isVisible={showShortcuts}
        onToggle={toggleShortcuts}
        shortcuts={defaultCanvasShortcuts}
        onShortcutExecute={handleShortcutExecute}
      />

      {/* Contextual Help System */}
      <ContextualHelpSystem
        isVisible={showHelp}
        onToggle={toggleHelp}
        currentContext={currentContext}
        onTopicSelect={(topicId) => console.log('Help topic selected:', topicId)}
      />

      {/* Onboarding Tour */}
      <OnboardingTour
        isVisible={showTour}
        onComplete={completeTour}
        onSkip={skipTour}      />
    </div>
  );
};

// Export the main component for lazy loading
export { LivingDataCanvas };

/**
 * LivingDataCanvas
 *
 * Main 2D/3D data visualization canvas for the app.
 * Handles force-directed layout, clustering, background effects, and user interaction.
 *
 * Performance: Consider breaking this file into smaller subcomponents/hooks.
 * TODO: Offload heavy calculations (e.g., force-directed layout) to a web worker using useDataProcessingWorker.
 */

// Enhanced Sample Data with modern, visually appealing attributes
const sampleData: CanvasData = {
  nodes: [
    { id: 'node1', label: '🎯 Analytics Hub', position: { x: -2, y: 3, z: 0 }, value: 92, category: 'core', tags: ['analytics', 'hub', 'central'] },
    { id: 'node2', label: '📊 Data Source', position: { x: 2, y: 4, z: 0 }, value: 78, category: 'input', tags: ['data', 'source', 'ingestion'] },
    { id: 'node4', label: '📈 Visualization', position: { x: 5, y: 2, z: 0 }, value: 88, category: 'output', tags: ['visualization', 'display', 'charts'] },
    { id: 'node5', label: '🖥️ User Interface', position: { x: -4, y: 5, z: 0 }, value: 95, category: 'interface', tags: ['ui', 'interaction', 'frontend'] },
    { id: 'node6', label: '💾 Storage Layer', position: { x: 1, y: -2, z: 0 }, value: 72, category: 'storage', tags: ['database', 'storage', 'persistence'] },
    { id: 'node7', label: '🔐 Security Module', position: { x: -3, y: 0, z: 0 }, value: 85, category: 'core', tags: ['security', 'auth', 'protection'] },
    { id: 'node8', label: '📡 API Gateway', position: { x: 3, y: -1, z: 0 }, value: 80, category: 'interface', tags: ['api', 'gateway', 'integration'] },
  ],
  connections: [
    { id: 'conn1', source: 'node1', target: 'node2', strength: 1.4, label: 'data ingestion' },
    { id: 'conn2', source: 'node2', target: 'node3', strength: 1.2, label: 'processing pipeline' },
    { id: 'conn3', source: 'node1', target: 'node3', strength: 1.1, label: 'orchestration' },
    { id: 'conn4', source: 'node3', target: 'node4', strength: 1.3, label: 'visualization data' },
    { id: 'conn5', source: 'node5', target: 'node1', strength: 1.6, label: 'user commands' },
    { id: 'conn6', source: 'node3', target: 'node6', strength: 1.2, label: 'data persistence' },
    { id: 'conn7', source: 'node6', target: 'node4', strength: 0.9, label: 'data retrieval' },
    { id: 'conn8', source: 'node7', target: 'node1', strength: 1.0, label: 'security layer' },
    { id: 'conn9', source: 'node8', target: 'node2', strength: 1.1, label: 'external data' },
    { id: 'conn10', source: 'node5', target: 'node4', strength: 1.5, label: 'display control' },
    { id: 'conn11', source: 'node7', target: 'node8', strength: 0.8, label: 'auth validation' },
  ],
};

export const LivingDataCanvasContainer: React.FC = () => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #0a0a1e 0%, #1a1a2e 50%, #16213e 100%)',
      padding: '20px',
      height: '100vh',
      boxSizing: 'border-box',
      position: 'relative',
      overflow: 'hidden',
    }}>
      {/* Modern header with glassmorphism effect */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.05)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        padding: '16px 24px',
        marginBottom: '20px',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
      }}>
        <h2 style={{
          color: 'white',
          textAlign: 'center',
          margin: '0',
          fontSize: '1.5rem',
          fontWeight: '600',
          background: 'linear-gradient(135deg, #ffffff 0%, #a5b4fc 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        }}>
          ✨ Living Data Canvas
        </h2>
        <p style={{
          color: 'rgba(255, 255, 255, 0.7)',
          textAlign: 'center',
          margin: '8px 0 0 0',
          fontSize: '0.9rem',
          fontWeight: '400',
        }}>
          Interactive 2D Data Visualization & Analysis Platform
        </p>
      </div>
      <LivingDataCanvas initialData={sampleData} />
    </div>
  );
};

// New container for Chat Analysis Canvas integration
export const ChatAnalysisCanvasContainer: React.FC = () => {
  return (
    <div style={{
      background: 'linear-gradient(135deg, #0a0a1e 0%, #1a1a2e 50%, #16213e 100%)',
      padding: '20px',
      height: '100vh',
      boxSizing: 'border-box',
      position: 'relative',
      overflow: 'hidden',
    }}>
      {/* Enhanced header for Chat Analysis Canvas */}
      <div style={{
        background: 'rgba(255, 255, 255, 0.05)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255, 255, 255, 0.1)',
        borderRadius: '16px',
        padding: '16px 24px',
        marginBottom: '20px',
        boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
      }}>
        <h2 style={{
          color: 'white',
          textAlign: 'center',
          margin: '0',
          fontSize: '1.5rem',
          fontWeight: '600',
          background: 'linear-gradient(135deg, #ffffff 0%, #a5b4fc 100%)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          backgroundClip: 'text',
        }}>
          🧠 Chat Analysis Canvas
        </h2>
        <p style={{
          color: 'rgba(255, 255, 255, 0.7)',
          textAlign: 'center',
          margin: '8px 0 0 0',
          fontSize: '0.9rem',
          fontWeight: '400',
        }}>
          Interactive Chat Analysis Record Visualization & Management
        </p>
      </div>
      <LivingDataCanvas enableChatAnalysisIntegration={true} />
    </div>
  );
};

export default LivingDataCanvasContainer;
