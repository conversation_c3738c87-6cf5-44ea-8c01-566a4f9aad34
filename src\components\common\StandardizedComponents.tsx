/**
 * Standardized Components
 * 
 * A collection of standardized components that follow consistent patterns
 * for props, styling, behavior, and accessibility across the application.
 */

import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import {
  StandardComponentProps,
  StandardInteractiveProps,
  StandardLayoutProps,
  StandardAnimationProps,
  createStandardClasses,
  createAnimationClasses,
  createStateClasses,
  createFocusClasses,
} from '@/utils/componentStandardization';
import { Loader2, AlertCircle, CheckCircle, Info, X } from 'lucide-react';

// Standardized Container Component
interface StandardContainerProps extends StandardComponentProps, StandardLayoutProps, StandardAnimationProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'ghost';
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  centered?: boolean;
  maxWidth?: boolean;
}

export const StandardContainer: React.FC<StandardContainerProps> = ({
  children,
  className,
  variant = 'default',
  size = 'md',
  centered = false,
  maxWidth = true,
  animate = true,
  ...props
}) => {
  const baseClasses = cn(
    'relative',
    // Variant styles
    variant === 'elevated' && 'bg-white shadow-lg border border-border/50 rounded-lg',
    variant === 'outlined' && 'border border-border rounded-lg',
    variant === 'ghost' && 'bg-transparent',
    variant === 'default' && 'bg-background',
    // Size styles
    size === 'sm' && 'p-4',
    size === 'md' && 'p-6',
    size === 'lg' && 'p-8',
    size === 'xl' && 'p-12',
    size === 'full' && 'w-full h-full',
    // Layout styles
    centered && 'mx-auto',
    maxWidth && 'max-w-7xl'
  );

  const standardClasses = createStandardClasses(baseClasses, props, className);
  const animationClasses = createAnimationClasses(props);

  const Component = animate ? motion.div : 'div';
  const motionProps = animate ? {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.3 }
  } : {};

  return (
    <Component
      className={cn(standardClasses, animationClasses)}
      {...motionProps}
      {...props}
    >
      {children}
    </Component>
  );
};

// Standardized Section Component
interface StandardSectionProps extends StandardComponentProps, StandardLayoutProps, StandardAnimationProps {
  title?: string;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
  actions?: React.ReactNode;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
}

export const StandardSection: React.FC<StandardSectionProps> = ({
  children,
  className,
  title,
  subtitle,
  icon: Icon,
  actions,
  collapsible = false,
  defaultCollapsed = false,
  animate = true,
  ...props
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed);

  const baseClasses = 'space-y-4';
  const standardClasses = createStandardClasses(baseClasses, props, className);
  const animationClasses = createAnimationClasses({ animate, ...props });

  return (
    <section className={cn(standardClasses, animationClasses)} {...props}>
      {(title || subtitle || Icon || actions) && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {Icon && (
              <div className="p-2 rounded-lg bg-primary/10">
                <Icon className="h-5 w-5 text-primary" />
              </div>
            )}
            <div>
              {title && (
                <h2 className="text-xl font-semibold text-foreground">{title}</h2>
              )}
              {subtitle && (
                <p className="text-sm text-muted-foreground">{subtitle}</p>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {actions}
            {collapsible && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="h-8 w-8 p-0"
              >
                {isCollapsed ? '+' : '−'}
              </Button>
            )}
          </div>
        </div>
      )}
      
      <AnimatePresence>
        {!isCollapsed && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  );
};

// Standardized Loading Component
interface StandardLoadingProps extends StandardComponentProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  overlay?: boolean;
}

export const StandardLoading: React.FC<StandardLoadingProps> = ({
  className,
  size = 'md',
  text,
  overlay = false,
  ...props
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8',
  };

  const baseClasses = cn(
    'flex items-center justify-center',
    overlay && 'absolute inset-0 bg-background/80 backdrop-blur-sm z-50'
  );

  return (
    <div className={cn(baseClasses, className)} {...props}>
      <div className="flex items-center gap-3">
        <Loader2 className={cn('animate-spin text-primary', sizeClasses[size])} />
        {text && (
          <span className="text-sm text-muted-foreground">{text}</span>
        )}
      </div>
    </div>
  );
};

// Standardized Error Component
interface StandardErrorProps extends StandardComponentProps {
  title?: string;
  message: string;
  action?: React.ReactNode;
  variant?: 'error' | 'warning' | 'info';
  dismissible?: boolean;
  onDismiss?: () => void;
}

export const StandardError: React.FC<StandardErrorProps> = ({
  className,
  title,
  message,
  action,
  variant = 'error',
  dismissible = false,
  onDismiss,
  ...props
}) => {
  const iconMap = {
    error: AlertCircle,
    warning: AlertCircle,
    info: Info,
  };

  const colorMap = {
    error: 'text-red-600 bg-red-50 border-red-200',
    warning: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    info: 'text-blue-600 bg-blue-50 border-blue-200',
  };

  const Icon = iconMap[variant];

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
      className={cn(
        'p-4 rounded-lg border',
        colorMap[variant],
        className
      )}
      {...props}
    >
      <div className="flex items-start gap-3">
        <Icon className="h-5 w-5 flex-shrink-0 mt-0.5" />
        <div className="flex-1 min-w-0">
          {title && (
            <h3 className="font-medium mb-1">{title}</h3>
          )}
          <p className="text-sm">{message}</p>
          {action && (
            <div className="mt-3">{action}</div>
          )}
        </div>
        {dismissible && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="h-6 w-6 p-0 hover:bg-current/10"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </motion.div>
  );
};

// Standardized Empty State Component
interface StandardEmptyStateProps extends StandardComponentProps {
  icon?: React.ComponentType<{ className?: string }>;
  title: string;
  description?: string;
  action?: React.ReactNode;
}

export const StandardEmptyState: React.FC<StandardEmptyStateProps> = ({
  className,
  icon: Icon,
  title,
  description,
  action,
  ...props
}) => {
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center py-12 text-center',
        className
      )}
      {...props}
    >
      {Icon && (
        <div className="mb-4 p-3 rounded-full bg-muted">
          <Icon className="h-8 w-8 text-muted-foreground" />
        </div>
      )}
      <h3 className="text-lg font-medium text-foreground mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-muted-foreground mb-4 max-w-sm">{description}</p>
      )}
      {action && action}
    </div>
  );
};

// Standardized Status Badge Component
interface StandardStatusBadgeProps extends StandardComponentProps {
  status: 'success' | 'error' | 'warning' | 'info' | 'pending';
  text?: string;
  size?: 'sm' | 'md';
}

export const StandardStatusBadge: React.FC<StandardStatusBadgeProps> = ({
  className,
  status,
  text,
  size = 'sm',
  ...props
}) => {
  const statusConfig = {
    success: { icon: CheckCircle, color: 'bg-green-100 text-green-800 border-green-200' },
    error: { icon: AlertCircle, color: 'bg-red-100 text-red-800 border-red-200' },
    warning: { icon: AlertCircle, color: 'bg-yellow-100 text-yellow-800 border-yellow-200' },
    info: { icon: Info, color: 'bg-blue-100 text-blue-800 border-blue-200' },
    pending: { icon: Loader2, color: 'bg-gray-100 text-gray-800 border-gray-200' },
  };

  const config = statusConfig[status];
  const Icon = config.icon;

  return (
    <Badge
      className={cn(
        'flex items-center gap-1 border',
        config.color,
        size === 'sm' ? 'text-xs px-2 py-1' : 'text-sm px-3 py-1.5',
        className
      )}
      {...props}
    >
      <Icon className={cn(
        status === 'pending' && 'animate-spin',
        size === 'sm' ? 'h-3 w-3' : 'h-4 w-4'
      )} />
      {text && <span>{text}</span>}
    </Badge>
  );
};
