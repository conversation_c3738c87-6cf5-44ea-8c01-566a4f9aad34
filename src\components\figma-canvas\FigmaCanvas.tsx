import React, { useRef, useEffect, useCallback } from 'react';
import { DrawingTool, Point } from '@/types/figma';
import { FigmaCanvasRenderer } from './FigmaCanvasRenderer';
import { FigmaCanvasEvents } from './FigmaCanvasEvents';
import { Canvas } from 'fabric';
import type * as fabric from 'fabric';
import { useFigmaCanvasStore } from '@/stores/useFigmaCanvasStore';
import { DrawingToolsFactory } from './tools/DrawingToolsFactory';

interface FigmaCanvasProps {
  className?: string;
  onSelectionChange?: (selectedIds: string[]) => void;
  onObjectCreated?: (objectId: string) => void;
  onObjectUpdated?: (objectId: string) => void;
  onObjectDeleted?: (objectId: string) => void;
}

export const FigmaCanvas: React.FC<FigmaCanvasProps> = ({
  className = '',
  onSelectionChange,
  onObjectCreated,
  onObjectUpdated,
  onObjectDeleted,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const canvasWrapperRef = useRef<HTMLDivElement>(null);
  const fabricCanvasRef = useRef<Canvas | null>(null);
  
  // Figma canvas store
  const {
    activeTool,
    selectedObjectIds,
    zoom,
    pan,
    gridVisible,
    snapToGrid,
    gridSize,
    canvasSize,
    backgroundColor,
    objects,
    layers,
    activeLayerId,
    addObject,
    selectObjects,
    clearSelection,
    setZoom,
    setPan,
    addEventListener,
  } = useFigmaCanvasStore();

  // Drawing state
  const isDrawingRef = useRef(false);
  const startPointRef = useRef<Point | null>(null);
  const currentPathRef = useRef<Point[]>([]);

  // Initialize Fabric.js canvas
  useEffect(() => {
    if (!canvasRef.current || !canvasWrapperRef.current) return;

    const fabricCanvas = new Canvas(canvasRef.current, {
      selection: activeTool === 'select',
      backgroundColor: backgroundColor,
      width: canvasSize.width,
      height: canvasSize.height,
      stopContextMenu: true,
      fireRightClick: true,
      fireMiddleClick: true,
    });

    fabricCanvasRef.current = fabricCanvas;

    // Set up resize observer
    const resizeObserver = new ResizeObserver(entries => {
      const { width, height } = entries[0].contentRect;
      fabricCanvas.setWidth(width);
      fabricCanvas.setHeight(height);
      fabricCanvas.renderAll();
    });

    resizeObserver.observe(canvasWrapperRef.current);

    return () => {
      resizeObserver.disconnect();
      fabricCanvas.dispose();
      fabricCanvasRef.current = null;
    };
  }, []);

  // Update canvas properties when store changes
  useEffect(() => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    canvas.selection = activeTool === 'select';
    canvas.backgroundColor = backgroundColor;
    canvas.setZoom(zoom);
    canvas.absolutePan(pan);
    canvas.renderAll();
  }, [activeTool, backgroundColor, zoom, pan]);

  // Handle mouse down events
  const handleMouseDown = useCallback((e: fabric.TEvent) => {
    if (!fabricCanvasRef.current) return;

    const canvas = fabricCanvasRef.current;
    const pointer = canvas.getPointer(e.e);
    const snappedPoint = DrawingToolsFactory.snapToGrid(pointer, gridSize, snapToGrid);

    if (activeTool === 'select') {
      // Handle selection logic
      if (!('target' in e) || !e.target) {
        return;
      }
      clearSelection();
      return;
    }

    // Start drawing
    isDrawingRef.current = true;
    startPointRef.current = snappedPoint;
    currentPathRef.current = [snappedPoint];

    // Prevent default fabric.js behavior for drawing tools
    e.e.preventDefault();
    e.e.stopPropagation();
  }, [activeTool, gridSize, snapToGrid, clearSelection]);

  // Handle mouse move events
  const handleMouseMove = useCallback((e: fabric.TEvent) => {
    if (!fabricCanvasRef.current || !isDrawingRef.current || !startPointRef.current) return;

    const canvas = fabricCanvasRef.current;
    const pointer = canvas.getPointer(e.e);
    const snappedPoint = DrawingToolsFactory.snapToGrid(pointer, gridSize, snapToGrid);

    if (activeTool === 'pen') {
      // Add point to path for pen tool
      currentPathRef.current.push(snappedPoint);
    }

    // Update preview (this would be implemented with temporary objects)
    canvas.renderAll();
  }, [activeTool, gridSize, snapToGrid]);

  // Handle mouse up events
  const handleMouseUp = useCallback((e: fabric.TEvent) => {
    if (!fabricCanvasRef.current || !isDrawingRef.current || !startPointRef.current) return;

    const canvas = fabricCanvasRef.current;
    const pointer = canvas.getPointer(e.e);
    const snappedPoint = DrawingToolsFactory.snapToGrid(pointer, gridSize, snapToGrid);

    try {
      let newObject;

      switch (activeTool) {
        case 'rectangle':
          newObject = DrawingToolsFactory.createRectangle(
            startPointRef.current,
            snappedPoint,
            activeLayerId
          );
          break;
        case 'circle': {
          const radius = DrawingToolsFactory.calculateDistance(startPointRef.current, snappedPoint);
          newObject = DrawingToolsFactory.createCircle(
            startPointRef.current,
            radius,
            activeLayerId
          );
          break;
        }
        case 'line':
          newObject = DrawingToolsFactory.createLine(
            [startPointRef.current, snappedPoint],
            activeLayerId
          );
          break;
        case 'arrow':
          newObject = DrawingToolsFactory.createArrow(
            startPointRef.current,
            snappedPoint,
            activeLayerId
          );
          break;
        case 'text':
          newObject = DrawingToolsFactory.createText(
            startPointRef.current,
            'Double-click to edit',
            activeLayerId
          );
          break;
        case 'pen':
          if (currentPathRef.current.length > 1) {
            newObject = DrawingToolsFactory.createPen(
              currentPathRef.current,
              activeLayerId
            );
          }
          break;
        case 'polygon': {
          const polygonRadius = DrawingToolsFactory.calculateDistance(startPointRef.current, snappedPoint);
          newObject = DrawingToolsFactory.createPolygon(
            startPointRef.current,
            polygonRadius,
            6, // Default to hexagon
            activeLayerId
          );
          break;
        }
        case 'star': {
          const starRadius = DrawingToolsFactory.calculateDistance(startPointRef.current, snappedPoint);
          newObject = DrawingToolsFactory.createStar(
            startPointRef.current,
            starRadius,
            starRadius * 0.5,
            5, // Default to 5-pointed star
            activeLayerId
          );
          break;
        }
      }

      if (newObject) {
        const objectId = addObject(newObject);
        onObjectCreated?.(objectId);
      }
    } catch (error) {
      console.error('Error creating object:', error);
    } finally {
      // Reset drawing state
      isDrawingRef.current = false;
      startPointRef.current = null;
      currentPathRef.current = [];
    }
  }, [activeTool, gridSize, snapToGrid, activeLayerId, addObject, onObjectCreated]);

  // Set up canvas event listeners
  useEffect(() => {
    const canvas = fabricCanvasRef.current;
    if (!canvas) return;

    canvas.on('mouse:down', handleMouseDown);
    canvas.on('mouse:move', handleMouseMove);
    canvas.on('mouse:up', handleMouseUp);

    return () => {
      canvas.off('mouse:down', handleMouseDown);
      canvas.off('mouse:move', handleMouseMove);
      canvas.off('mouse:up', handleMouseUp);
    };
  }, [handleMouseDown, handleMouseMove, handleMouseUp]);

  // Set up store event listeners
  useEffect(() => {
    const unsubscribe = addEventListener((event) => {
      switch (event.type) {
        case 'selection-changed':
          onSelectionChange?.(event.data.selectedObjectIds);
          break;
        case 'object-created':
          onObjectCreated?.(event.data.objectId);
          break;
        case 'object-updated':
          onObjectUpdated?.(event.data.objectId);
          break;
        case 'object-deleted':
          onObjectDeleted?.(event.data.objectId);
          break;
      }
    });

    return unsubscribe;
  }, [addEventListener, onSelectionChange, onObjectCreated, onObjectUpdated, onObjectDeleted]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.target !== document.body) return; // Only handle when not in input fields

      switch (e.key) {
        case 'Delete':
        case 'Backspace':
          if (selectedObjectIds.length > 0) {
            // Delete selected objects
            selectedObjectIds.forEach(id => {
              // This would be handled by the store
            });
          }
          break;
        case 'Escape':
          clearSelection();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [selectedObjectIds, clearSelection]);

  return (
    <div 
      ref={canvasWrapperRef}
      className={`figma-canvas-wrapper relative w-full h-full overflow-hidden ${className}`}
      style={{ cursor: getCursorForTool(activeTool) }}
    >
      <canvas ref={canvasRef} />
      
      {/* Grid overlay */}
      {gridVisible && (
        <div 
          className="absolute inset-0 pointer-events-none"
          style={{
            backgroundImage: `
              linear-gradient(to right, rgba(0,0,0,0.1) 1px, transparent 1px),
              linear-gradient(to bottom, rgba(0,0,0,0.1) 1px, transparent 1px)
            `,
            backgroundSize: `${gridSize * zoom}px ${gridSize * zoom}px`,
            backgroundPosition: `${pan.x}px ${pan.y}px`,
          }}
        />
      )}
      
      {/* Render objects using FigmaCanvasRenderer */}
      <FigmaCanvasRenderer 
        canvas={fabricCanvasRef.current}
        objects={objects}
        layers={layers}
        selectedObjectIds={selectedObjectIds}
      />
      
      {/* Handle canvas events */}
      <FigmaCanvasEvents 
        canvas={fabricCanvasRef.current}
        onZoomChange={setZoom}
        onPanChange={setPan}
      />
    </div>
  );
};

function getCursorForTool(tool: DrawingTool): string {
  const cursors: Record<DrawingTool, string> = {
    select: 'default',
    rectangle: 'crosshair',
    circle: 'crosshair',
    line: 'crosshair',
    arrow: 'crosshair',
    text: 'text',
    pen: 'crosshair',
    polygon: 'crosshair',
    star: 'crosshair',
    image: 'crosshair',
    'bezier-curve': 'crosshair',
    path: 'crosshair',
    'boolean-operation': 'pointer',
  };
  return cursors[tool] || 'default';
}
